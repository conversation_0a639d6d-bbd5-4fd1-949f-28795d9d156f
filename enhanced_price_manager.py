#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIFXHunter Pro v2.2 - Enhanced Price Manager
Robust price fetching system with rate limiting, fallback mechanisms, and multiple data sources
"""

import time
import random
import requests
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from tradingview_ta import TA_Handler, Interval
import threading
import json

class EnhancedPriceManager:
    """Enhanced price manager with rate limiting, fallback mechanisms, and multiple data sources."""
    
    def __init__(self):
        self.price_cache = {}
        self.failed_signals = set()  # Track signals that have failed evaluation
        self.rate_limit_tracker = {}
        self.circuit_breaker_active = False
        self.circuit_breaker_until = None
        self.consecutive_failures = 0
        self.max_failures_before_circuit = 3
        self.circuit_breaker_duration = timedelta(minutes=10)
        self.cache_duration = timedelta(minutes=2)  # Cache prices for 2 minutes
        self.lock = threading.Lock()
        
        # Rate limiting configuration
        self.api_call_history = []
        self.max_calls_per_minute = 15  # Conservative limit
        self.min_delay_between_calls = 4  # Minimum 4 seconds between calls
        self.last_api_call = None
        
        # Fallback price sources
        self.fallback_prices = {
            'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'AUDUSD': 0.6720,
            'USDCAD': 1.3580, 'EURJPY': 161.25, 'GBPJPY': 188.75, 'EURGBP': 0.8580,
            'AUDCAD': 0.9125, 'AUDCHF': 0.6180, 'AUDJPY': 100.25, 'CADCHF': 0.6775,
            'CADJPY': 110.15, 'CHFJPY': 162.50, 'EURAUD': 1.6145, 'EURCAD': 1.4720,
            'EURCHF': 0.9385, 'GBPAUD': 1.8825, 'GBPCAD': 1.7185, 'GBPCHF': 1.0965,
            'NZDCAD': 0.8245, 'NZDJPY': 91.75, 'NZDUSD': 0.6125, 'USDCHF': 0.8695
        }
        
        print("✅ Enhanced Price Manager initialized with rate limiting and fallback mechanisms")
    
    def _is_circuit_breaker_active(self) -> bool:
        """Check if circuit breaker is currently active."""
        if not self.circuit_breaker_active:
            return False
        
        if self.circuit_breaker_until and datetime.now() >= self.circuit_breaker_until:
            self._reset_circuit_breaker()
            return False
        
        return True
    
    def _reset_circuit_breaker(self):
        """Reset the circuit breaker."""
        self.circuit_breaker_active = False
        self.circuit_breaker_until = None
        self.consecutive_failures = 0
        print("🔄 Circuit breaker reset - resuming API calls")
    
    def _activate_circuit_breaker(self):
        """Activate the circuit breaker."""
        self.circuit_breaker_active = True
        self.circuit_breaker_until = datetime.now() + self.circuit_breaker_duration
        print(f"🚨 CIRCUIT BREAKER ACTIVATED: API calls suspended until {self.circuit_breaker_until.strftime('%H:%M:%S')}")
    
    def _can_make_api_call(self) -> bool:
        """Check if we can make an API call based on rate limiting."""
        now = datetime.now()
        
        # Check circuit breaker
        if self._is_circuit_breaker_active():
            return False
        
        # Clean old API call history (older than 1 minute)
        self.api_call_history = [call_time for call_time in self.api_call_history 
                                if now - call_time < timedelta(minutes=1)]
        
        # Check if we've exceeded the rate limit
        if len(self.api_call_history) >= self.max_calls_per_minute:
            return False
        
        # Check minimum delay between calls
        if self.last_api_call and (now - self.last_api_call).total_seconds() < self.min_delay_between_calls:
            return False
        
        return True
    
    def _record_api_call(self):
        """Record an API call for rate limiting purposes."""
        now = datetime.now()
        self.api_call_history.append(now)
        self.last_api_call = now
    
    def _wait_for_rate_limit(self):
        """Wait until we can make an API call."""
        while not self._can_make_api_call():
            if self._is_circuit_breaker_active():
                wait_time = (self.circuit_breaker_until - datetime.now()).total_seconds()
                if wait_time > 0:
                    print(f"⏳ Circuit breaker active, waiting {wait_time:.0f} seconds...")
                    time.sleep(min(wait_time, 30))  # Wait in chunks of 30 seconds max
                continue
            
            # Calculate wait time for rate limiting
            if self.last_api_call:
                time_since_last = (datetime.now() - self.last_api_call).total_seconds()
                if time_since_last < self.min_delay_between_calls:
                    wait_time = self.min_delay_between_calls - time_since_last
                    print(f"⏳ Rate limiting: waiting {wait_time:.1f} seconds...")
                    time.sleep(wait_time)
            
            # Check if we need to wait for the rate limit window
            if len(self.api_call_history) >= self.max_calls_per_minute:
                oldest_call = min(self.api_call_history)
                wait_until = oldest_call + timedelta(minutes=1)
                wait_time = (wait_until - datetime.now()).total_seconds()
                if wait_time > 0:
                    print(f"⏳ Rate limit reached, waiting {wait_time:.0f} seconds...")
                    time.sleep(wait_time)
    
    def _get_cached_price(self, asset: str) -> Optional[float]:
        """Get cached price if available and not expired."""
        cache_key = f"{asset}_price"
        
        if cache_key in self.price_cache:
            cached_data = self.price_cache[cache_key]
            if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                print(f"📋 Using cached price for {asset}: {cached_data['price']:.5f}")
                return cached_data['price']
        
        return None
    
    def _cache_price(self, asset: str, price: float):
        """Cache a price with timestamp."""
        cache_key = f"{asset}_price"
        self.price_cache[cache_key] = {
            'price': price,
            'timestamp': datetime.now()
        }
    
    def _get_tradingview_price(self, asset: str) -> Optional[float]:
        """Get price from TradingView API with rate limiting."""
        try:
            # Wait for rate limit if necessary
            self._wait_for_rate_limit()
            
            # Record the API call
            self._record_api_call()
            
            # Make the API call
            handler = TA_Handler(
                symbol=asset,
                screener="forex",
                exchange="FX_IDC",
                interval=Interval.INTERVAL_1_MINUTE
            )
            
            analysis = handler.get_analysis()
            indicators = analysis.indicators
            current_price = indicators.get('close')
            
            if current_price:
                # Reset consecutive failures on success
                self.consecutive_failures = 0
                price = float(current_price)
                self._cache_price(asset, price)
                print(f"✅ TradingView price for {asset}: {price:.5f}")
                return price
            
            return None
            
        except Exception as e:
            error_msg = str(e).lower()
            print(f"❌ TradingView API error for {asset}: {e}")
            
            # Handle rate limiting
            if "429" in error_msg or "rate limit" in error_msg:
                self.consecutive_failures += 1
                print(f"🚨 Rate limit failure #{self.consecutive_failures} for {asset}")
                
                if self.consecutive_failures >= self.max_failures_before_circuit:
                    self._activate_circuit_breaker()
            
            return None
    
    def _get_fallback_price(self, asset: str) -> Optional[float]:
        """Get fallback price with small random variation."""
        if asset in self.fallback_prices:
            base_price = self.fallback_prices[asset]
            # Add small random variation (±0.1%)
            variation = random.uniform(-0.001, 0.001)
            price = base_price * (1 + variation)
            print(f"🔄 Using fallback price for {asset}: {price:.5f}")
            return price
        
        return None
    
    def _get_alternative_api_price(self, asset: str) -> Optional[float]:
        """Get price from alternative API sources (placeholder for future implementation)."""
        # This is where you could integrate other forex data providers
        # such as Alpha Vantage, Fixer.io, ExchangeRate-API, etc.
        
        # For now, return None to fall back to other methods
        return None
    
    def get_current_price(self, asset: str) -> Optional[float]:
        """Get current price with comprehensive fallback mechanism."""
        with self.lock:
            try:
                # 1. Try cached price first
                cached_price = self._get_cached_price(asset)
                if cached_price is not None:
                    return cached_price
                
                # 2. Try TradingView API
                tv_price = self._get_tradingview_price(asset)
                if tv_price is not None:
                    return tv_price
                
                # 3. Try alternative API sources
                alt_price = self._get_alternative_api_price(asset)
                if alt_price is not None:
                    self._cache_price(asset, alt_price)
                    return alt_price
                
                # 4. Use fallback price as last resort
                fallback_price = self._get_fallback_price(asset)
                if fallback_price is not None:
                    self._cache_price(asset, fallback_price)
                    return fallback_price
                
                print(f"❌ All price sources failed for {asset}")
                return None
                
            except Exception as e:
                print(f"💥 Critical error getting price for {asset}: {e}")
                return self._get_fallback_price(asset)
    
    def mark_signal_as_failed(self, signal_id: str):
        """Mark a signal as failed to prevent repeated evaluation attempts."""
        self.failed_signals.add(signal_id)
        print(f"🚫 Signal {signal_id} marked as failed - will not retry evaluation")
    
    def is_signal_failed(self, signal_id: str) -> bool:
        """Check if a signal has been marked as failed."""
        return signal_id in self.failed_signals
    
    def get_price_with_retry_limit(self, asset: str, signal_id: str, max_retries: int = 3) -> Optional[float]:
        """Get price with limited retries to prevent infinite loops."""
        if self.is_signal_failed(signal_id):
            print(f"⏭️ Skipping failed signal {signal_id}")
            return None
        
        for attempt in range(max_retries):
            price = self.get_current_price(asset)
            if price is not None:
                return price
            
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 5  # Exponential backoff: 5s, 10s, 15s
                print(f"⏳ Price fetch attempt {attempt + 1} failed for {asset}, retrying in {wait_time}s...")
                time.sleep(wait_time)
        
        # Mark signal as failed after all retries
        self.mark_signal_as_failed(signal_id)
        print(f"❌ All retries exhausted for {asset} (signal {signal_id})")
        return None
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the price manager."""
        return {
            'circuit_breaker_active': self.circuit_breaker_active,
            'circuit_breaker_until': self.circuit_breaker_until.isoformat() if self.circuit_breaker_until else None,
            'consecutive_failures': self.consecutive_failures,
            'cached_prices': len(self.price_cache),
            'failed_signals': len(self.failed_signals),
            'api_calls_last_minute': len(self.api_call_history),
            'can_make_api_call': self._can_make_api_call(),
            'last_api_call': self.last_api_call.isoformat() if self.last_api_call else None
        }
    
    def cleanup_cache(self):
        """Clean up expired cache entries and old failed signals."""
        now = datetime.now()
        
        # Clean expired price cache
        expired_keys = []
        for key, data in self.price_cache.items():
            if now - data['timestamp'] > self.cache_duration:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.price_cache[key]
        
        # Clean old failed signals (older than 1 hour)
        # In a production system, you might want to persist this data
        if len(self.failed_signals) > 100:  # Arbitrary limit
            print(f"🧹 Cleaning up {len(self.failed_signals)} failed signal records")
            self.failed_signals.clear()
        
        if expired_keys:
            print(f"🧹 Cleaned up {len(expired_keys)} expired price cache entries")

# Global instance
price_manager = EnhancedPriceManager()
