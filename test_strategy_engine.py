#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIFXHunter Pro v2.2 - Strategy Engine Test
Quick test script to demonstrate the new AI strategy capabilities
"""

from strategy_engine import StrategyEngine
from indicator_calculator import IndicatorCalculator
from strategy_templates import StrategyTemplates
from signal_generator import SignalGenerator
import pandas as pd
import numpy as np

def test_strategy_engine():
    """Test the strategy engine with sample data."""
    print("🤖 Testing AIFXHunter Pro v2.2 Strategy Engine")
    print("=" * 50)
    
    # Initialize components
    strategy_engine = StrategyEngine()
    indicator_calculator = IndicatorCalculator()
    strategy_templates = StrategyTemplates()
    signal_generator = SignalGenerator()
    
    print("✅ All components initialized successfully")
    
    # Test 1: Parse a custom strategy
    print("\n📝 Test 1: Custom Strategy Parsing")
    custom_strategy = "If RSI is below 30 and MACD shows bullish crossover, then generate CALL signal"
    parsed = strategy_engine.parse_strategy(custom_strategy)
    
    print(f"Strategy: {custom_strategy}")
    print(f"Valid: {parsed.get('valid', False)}")
    print(f"Conditions: {len(parsed.get('conditions', []))}")
    print(f"Actions: {len(parsed.get('actions', []))}")
    
    # Test 2: Strategy validation
    print("\n✅ Test 2: Strategy Validation")
    validation = strategy_engine.validate_strategy(custom_strategy)
    print(f"Valid: {validation['valid']}")
    if validation['errors']:
        print(f"Errors: {validation['errors']}")
    if validation['suggestions']:
        print(f"Suggestions: {validation['suggestions']}")
    
    # Test 3: Template library
    print("\n📋 Test 3: Strategy Templates")
    templates = strategy_templates.get_all_templates()
    print(f"Available templates: {len(templates)}")
    
    # Show a few templates
    for i, (tid, template) in enumerate(list(templates.items())[:3]):
        print(f"{i+1}. {template['name']} ({template['category']}) - {template['success_rate']}")
    
    # Test 4: Indicator calculation
    print("\n📊 Test 4: Technical Indicators")
    
    # Generate sample OHLC data
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=100, freq='5min')
    
    # Create realistic forex price data
    base_price = 1.0800
    price_changes = np.random.normal(0, 0.001, 100)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # Create OHLC data
    opens = prices[:-1]
    closes = prices[1:]
    highs = [max(o, c) * (1 + abs(np.random.normal(0, 0.0005))) for o, c in zip(opens, closes)]
    lows = [min(o, c) * (1 - abs(np.random.normal(0, 0.0005))) for o, c in zip(opens, closes)]
    
    sample_data = pd.DataFrame({
        'datetime': dates[1:],
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': np.random.randint(1000, 10000, len(closes))
    })
    
    # Calculate indicators
    indicators = indicator_calculator.calculate_all_indicators(sample_data, "EURUSD")
    
    print(f"RSI: {indicators.get('rsi', 0):.2f}")
    print(f"MACD: {indicators.get('macd', 0):.4f}")
    print(f"MACD Signal: {indicators.get('macd_signal', 0):.4f}")
    print(f"EMA(21): {indicators.get('ema_21', 0):.4f}")
    print(f"Bollinger Upper: {indicators.get('bb_upper', 0):.4f}")
    print(f"Stochastic %K: {indicators.get('stoch_k', 0):.2f}")
    print(f"Williams %R: {indicators.get('williams_r', 0):.2f}")
    
    # Test 5: Strategy evaluation
    print("\n🎯 Test 5: Strategy Evaluation")
    
    # Test with RSI oversold strategy
    rsi_strategy = "If RSI is below 30, then generate CALL signal"
    parsed_rsi = strategy_engine.parse_strategy(rsi_strategy)
    
    # Modify indicators to trigger the strategy
    test_indicators = indicators.copy()
    test_indicators['rsi'] = 25  # Force oversold condition
    
    evaluation = strategy_engine.evaluate_strategy(parsed_rsi, test_indicators)
    
    print(f"Strategy: {rsi_strategy}")
    print(f"RSI Value: {test_indicators['rsi']}")
    print(f"Conditions Met: {evaluation.get('all_conditions_met', False)}")
    print(f"Confidence: {evaluation.get('confidence', 0):.1f}%")
    print(f"Reasoning: {evaluation.get('reasoning', [])}")
    
    # Test 6: Signal generation
    print("\n🚀 Test 6: Signal Generation")
    
    if evaluation.get('all_conditions_met', False):
        signal = signal_generator.generate_signal(
            symbol="EURUSD",
            market_data=test_indicators,
            custom_strategy=rsi_strategy
        )
        
        if signal:
            print("✅ Signal Generated!")
            print(f"Asset: {signal['asset']}")
            print(f"Action: {signal['signal']}")
            print(f"Expiry: {signal['expiry']}")
            print(f"Confidence: {signal['confidence']}")
            print(f"Entry Price: {signal['entry_price']}")
            print(f"Strategy: {signal['strategy_name']}")
            print(f"Reasoning: {signal['reasoning'][:2]}")  # First 2 reasons
        else:
            print("❌ No signal generated")
    
    # Test 7: Template strategy
    print("\n📋 Test 7: Template Strategy Test")
    
    # Get RSI oversold template
    rsi_template = strategy_templates.get_template('rsi_oversold_bounce')
    if rsi_template:
        print(f"Template: {rsi_template['name']}")
        print(f"Description: {rsi_template['description']}")
        print(f"Success Rate: {rsi_template['success_rate']}")
        
        # Test the template strategy
        template_signal = signal_generator.generate_signal(
            symbol="EURUSD",
            market_data=test_indicators,
            strategy_id='rsi_oversold_bounce'
        )
        
        if template_signal:
            print("✅ Template Signal Generated!")
            print(f"Confidence: {template_signal['confidence']}")
            print(f"Risk Level: {template_signal['risk_level']}")
    
    print("\n🎉 Strategy Engine Test Complete!")
    print("All components are working correctly.")

if __name__ == "__main__":
    test_strategy_engine()
