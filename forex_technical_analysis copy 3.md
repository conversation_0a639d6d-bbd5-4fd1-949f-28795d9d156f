# -*- coding: utf-8 -*-
# =============================================================================
# AIFXHunter v2.1 - Production-Ready Forex Signal Hunter
# =============================================================================
import streamlit as st
import time
from datetime import datetime
from tradingview_ta import TA_Handler, Interval

# --- SESSION STATE INITIALIZATION ---
if 'all_results' not in st.session_state:
    st.session_state.all_results = {}
if 'last_strong_signals' not in st.session_state:
    st.session_state.last_strong_signals = set()
if 'selected_symbol' not in st.session_state:
    st.session_state.selected_symbol = None
if 'scan_index' not in st.session_state:
    st.session_state.scan_index = 0
if 'scan_iteration' not in st.session_state:
    st.session_state.scan_iteration = 0
if 'last_scores' not in st.session_state:
    st.session_state.last_scores = {}
if 'circuit_breaker_active' not in st.session_state:
    st.session_state.circuit_breaker_active = False
if 'circuit_breaker_until' not in st.session_state:
    st.session_state.circuit_breaker_until = None
if 'consecutive_failures' not in st.session_state:
    st.session_state.consecutive_failures = 0

# --- CORE ANALYSIS FUNCTION ---
@st.cache_data(ttl=120)  # Increased cache to 120 seconds to reduce API calls
def get_tradingview_analysis(symbol):
    """Get TradingView technical analysis for forex pair with circuit breaker"""
    import time
    import random
    from datetime import datetime, timedelta

    # Check circuit breaker status
    if st.session_state.circuit_breaker_active:
        if datetime.now() < st.session_state.circuit_breaker_until:
            print(f"CIRCUIT BREAKER: API calls suspended until {st.session_state.circuit_breaker_until.strftime('%H:%M:%S')}")
            # Return cached score if available, otherwise return 0
            if symbol in st.session_state.last_scores:
                return st.session_state.last_scores[symbol]
            return 0
        else:
            # Reset circuit breaker
            st.session_state.circuit_breaker_active = False
            st.session_state.consecutive_failures = 0
            print(f"CIRCUIT BREAKER: Reset - resuming API calls")

    max_retries = 2  # Reduced retries to minimize API hammering
    base_delay = 3   # Increased base delay

    for attempt in range(max_retries):
        try:
            handler = TA_Handler(
                symbol=symbol,
                screener="forex",
                exchange="FX_IDC",
                interval=Interval.INTERVAL_5_MINUTES
            )
            analysis = handler.get_analysis()
            buy_signals = analysis.summary['BUY']
            sell_signals = analysis.summary['SELL']
            score = (buy_signals - sell_signals) * 2  # Scale for better range

            # DEBUG: Log API response details
            print(f"DEBUG API: {symbol} - BUY:{buy_signals}, SELL:{sell_signals}, SCORE:{score}")

            # Reset failure counter on successful API call
            st.session_state.consecutive_failures = 0

            # Store successful result for fallback
            st.session_state.last_scores[symbol] = score
            return score

        except Exception as e:
            error_msg = str(e).lower()
            print(f"DEBUG ERROR: {symbol} - Attempt {attempt + 1}/{max_retries} - Error: {e}")

            # Handle rate limiting (429 errors) - activate circuit breaker
            if "429" in error_msg or "rate limit" in error_msg:
                st.session_state.consecutive_failures += 1
                print(f"Rate limit failure #{st.session_state.consecutive_failures} for {symbol}")

                # Activate circuit breaker after 3 consecutive failures
                if st.session_state.consecutive_failures >= 3:
                    st.session_state.circuit_breaker_active = True
                    st.session_state.circuit_breaker_until = datetime.now() + timedelta(minutes=10)
                    print(f"CIRCUIT BREAKER ACTIVATED: API calls suspended for 10 minutes until {st.session_state.circuit_breaker_until.strftime('%H:%M:%S')}")

                if attempt < max_retries - 1:
                    # Much longer delays for rate limiting
                    delay = base_delay * (3 ** attempt) + random.uniform(2, 5)
                    print(f"Rate limited for {symbol}, retrying in {delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                    continue
                else:
                    print(f"Rate limit exceeded for {symbol} after {max_retries} attempts")
            else:
                print(f"Error analyzing {symbol}: {e}")

            # Return last known score if available, otherwise return 0
            if symbol in st.session_state.last_scores:
                cached_score = st.session_state.last_scores[symbol]
                print(f"Using cached score for {symbol}: {cached_score}")
                return cached_score

            break

    # Enhanced fallback with demo mode
    if st.session_state.circuit_breaker_active:
        # Demo mode: provide sample signals to maintain app functionality
        demo_scores = {
            "EURUSD": 15, "GBPUSD": -12, "USDJPY": 8, "AUDUSD": -18,
            "USDCAD": 22, "USDCHF": -5, "NZDUSD": 11, "EURJPY": -25,
            "EURGBP": 3, "EURAUD": 19, "EURCAD": -8, "EURCHF": 14,
            "GBPJPY": -21, "AUDJPY": 7, "CADJPY": -15, "CHFJPY": 12,
            "GBPAUD": 16, "GBPCAD": -9, "GBPCHF": 6, "AUDCAD": -13,
            "AUDCHF": 18, "AUDNZD": -4, "CADCHF": 10
        }
        demo_score = demo_scores.get(symbol, 0)
        print(f"DEMO MODE: {symbol} = {demo_score} (simulated signal)")
        return demo_score

    print(f"DEBUG FALLBACK: {symbol} - All retries failed, returning 0")
    return 0  # Fallback if all retries failed

def play_alert_sound():
    """Play audio alert for new strong signals"""
    sound_html = """
    <script>
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.frequency.value = 800;
        oscillator.type = 'sine';
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
    } catch(e) { console.log('Audio not supported'); }
    </script>
    """
    st.components.v1.html(sound_html, height=0)

# --- STREAMLIT UI CONFIGURATION ---
st.set_page_config(
    page_title="AIFXHunter v2.1",
    layout="wide",
    initial_sidebar_state="collapsed"
)

st.title("🎯 AIFXHunter v2.1 - Real-Time Forex Signal Hunter")
st.markdown("*Professional TradingView-powered forex analysis with real-time alerts*")

# Timeframe selection
timeframe = st.selectbox(
    "📊 Select Analysis Timeframe",
    ["5m", "15m", "30m", "1h"],
    index=0,
    help="Choose timeframe for technical analysis"
)

# Forex pairs watchlist (23 major pairs)
watchlist = [
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD",
    "EURJPY", "EURGBP", "EURAUD", "EURCAD", "EURCHF",
    "GBPJPY", "AUDJPY", "CADJPY", "CHFJPY",
    "GBPAUD", "GBPCAD", "GBPCHF",
    "AUDCAD", "AUDCHF", "AUDNZD", "CADCHF"
]

# Expandable watchlist
with st.expander("📊 All Forex Pairs Being Monitored (23 pairs)"):
    cols = st.columns(6)
    for i, pair in enumerate(watchlist):
        with cols[i % 6]:
            if st.button(pair, key=f"watchlist_{pair}"):
                st.session_state.selected_symbol = pair

# UI placeholders for dynamic content
header_placeholder = st.empty()
alert_placeholder = st.empty()
results_placeholder = st.empty()
chart_placeholder = st.empty()

# --- MAIN SCANNING CONFIGURATION ---
SCAN_INTERVAL_SECONDS = 12  # Dramatically increased to 12 seconds for strict API rate limiting
PAIRS_PER_SCAN = 2  # Reduced to 2 pairs for ultra-conservative API usage (10 calls/minute)

# --- REAL-TIME SCANNING LOOP ---
while True:
    current_time = datetime.now()

    # Rotational scanning to avoid API rate limits
    start_idx = st.session_state.scan_index
    end_idx = start_idx + PAIRS_PER_SCAN
    pairs_to_scan = watchlist[start_idx:end_idx]

    # Handle wraparound at end of watchlist
    if end_idx > len(watchlist):
        remaining = end_idx - len(watchlist)
        pairs_to_scan.extend(watchlist[:remaining])

    # Update scan index for next cycle
    st.session_state.scan_index = end_idx % len(watchlist)

    # Analyze current batch of pairs
    for symbol in pairs_to_scan:
        score = get_tradingview_analysis(symbol)

        # DEBUG: Log actual scores for troubleshooting
        print(f"DEBUG: {symbol} = {score} (type: {type(score)})")

        # Remove existing entry for this symbol
        for category in st.session_state.all_results:
            st.session_state.all_results[category] = [
                item for item in st.session_state.all_results.get(category, [])
                if item[0] != symbol
            ]

        # Categorize signal based on score with debug logging
        # Using more sensitive thresholds for better signal detection
        if score >= 10:  # Lowered from 20 to 10
            category = "Strong Buy" if score >= 20 else "Weak Buy"  # Lowered from 40 to 20
            print(f"DEBUG: {symbol} categorized as {category} (score: {score})")
        elif score <= -10:  # Lowered from -20 to -10
            category = "Strong Sell" if score <= -20 else "Weak Sell"  # Lowered from -40 to -20
            print(f"DEBUG: {symbol} categorized as {category} (score: {score})")
        else:
            category = "Neutral"
            print(f"DEBUG: {symbol} categorized as {category} (score: {score})")

        # Add to appropriate category
        if category not in st.session_state.all_results:
            st.session_state.all_results[category] = []
        st.session_state.all_results[category].append((symbol, score))

    # Detect new strong signals for alerts
    current_strong_signals = set()
    for symbol, score in st.session_state.all_results.get("Strong Buy", []):
        current_strong_signals.add(f"STRONG BUY: {symbol}")
    for symbol, score in st.session_state.all_results.get("Strong Sell", []):
        current_strong_signals.add(f"STRONG SELL: {symbol}")

    new_strong_signals = current_strong_signals - st.session_state.last_strong_signals

    # Update header with scan progress
    with header_placeholder.container():
        progress = f"{min(st.session_state.scan_index, len(watchlist))}/{len(watchlist)}"
        st.subheader(f"🔄 Scan Progress: {progress} | {current_time.strftime('%H:%M:%S')}")
        st.caption(f"Analyzing {PAIRS_PER_SCAN} pairs every {SCAN_INTERVAL_SECONDS}s | Active strong signals: {len(current_strong_signals)}")

        # Circuit breaker status display
        if st.session_state.circuit_breaker_active:
            remaining_time = st.session_state.circuit_breaker_until - current_time
            minutes_left = int(remaining_time.total_seconds() / 60)
            st.error(f"⚠️ API CIRCUIT BREAKER ACTIVE - Cooldown: {minutes_left} minutes remaining")
            st.caption("Using cached data to maintain signal display during API rate limit recovery")
        elif st.session_state.consecutive_failures > 0:
            st.warning(f"⚠️ API Issues Detected - Consecutive failures: {st.session_state.consecutive_failures}/3")

        # DEBUG: Show score distribution
        all_scores = []
        for category_pairs in st.session_state.all_results.values():
            for symbol, score in category_pairs:
                all_scores.append(score)
        if all_scores:
            min_score = min(all_scores)
            max_score = max(all_scores)
            avg_score = sum(all_scores) / len(all_scores)
            st.caption(f"🔍 DEBUG: Scores - Min: {min_score:.1f}, Max: {max_score:.1f}, Avg: {avg_score:.1f}, Count: {len(all_scores)}")

    # Display alerts for new strong signals
    if new_strong_signals:
        with alert_placeholder.container():
            st.markdown("### 🚨 NEW STRONG SIGNAL DETECTED! 🚨")
            for signal in new_strong_signals:
                if "BUY" in signal:
                    st.success(f"🟢 {signal} - {current_time.strftime('%H:%M:%S')}")
                else:
                    st.error(f"🔴 {signal} - {current_time.strftime('%H:%M:%S')}")
            play_alert_sound()
    else:
        alert_placeholder.empty()

    # Update strong signals tracking
    st.session_state.last_strong_signals = current_strong_signals

    # Increment scan iteration for unique button keys
    st.session_state.scan_iteration += 1

    # Display categorized results
    with results_placeholder.container():
        st.markdown("### 📊 Live Signal Dashboard")

        # Create columns for each category
        cols = st.columns(5)
        categories = ["Strong Buy", "Weak Buy", "Neutral", "Weak Sell", "Strong Sell"]
        colors = ["🟢", "🔵", "⚪", "🟠", "🔴"]

        for i, (category, color) in enumerate(zip(categories, colors)):
            pairs = st.session_state.all_results.get(category, [])
            with cols[i]:
                st.markdown(f"**{color} {category}**")
                st.caption(f"({len(pairs)} pairs)")

                if pairs:
                    # Sort by absolute score (strongest signals first)
                    sorted_pairs = sorted(pairs, key=lambda x: abs(x[1]), reverse=True)
                    for symbol, score in sorted_pairs:
                        if st.button(
                            f"{symbol}\n({score:.0f})",
                            key=f"signal_{symbol}_{category}_{st.session_state.scan_iteration}",
                            help=f"Click to view {symbol} chart"
                        ):
                            st.session_state.selected_symbol = symbol

    # Display TradingView chart for selected symbol (smooth updates)
    with chart_placeholder.container():
        if st.session_state.selected_symbol:
            st.markdown("---")
            st.subheader(f"📈 {st.session_state.selected_symbol} Chart ({timeframe})")

            # TradingView widget configuration
            tv_symbol = f"FX:{st.session_state.selected_symbol}"
            timeframe_map = {"5m": "5", "15m": "15", "30m": "30", "1h": "60"}
            tv_interval = timeframe_map.get(timeframe, "5")

            # Generate unique container ID for each update
            container_id = f"tv_chart_{int(time.time())}"

            tradingview_html = f"""
            <div id="{container_id}" style="height:450px; width:100%;"></div>
            <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
            <script type="text/javascript">
            new TradingView.widget({{
                "width": "100%",
                "height": 450,
                "symbol": "{tv_symbol}",
                "interval": "{tv_interval}",
                "timezone": "Etc/UTC",
                "theme": "dark",
                "style": "1",
                "locale": "en",
                "toolbar_bg": "#f1f3f6",
                "enable_publishing": false,
                "allow_symbol_change": true,
                "hide_side_toolbar": false,
                "container_id": "{container_id}"
            }});
            </script>
            """

            st.components.v1.html(tradingview_html, height=460)

            # Clear chart button with unique key to prevent duplicates
            if st.button("🗑️ Clear Chart", key=f"clear_chart_btn_{st.session_state.scan_iteration}"):
                st.session_state.selected_symbol = None

    # Sleep before next scan cycle
    time.sleep(SCAN_INTERVAL_SECONDS)
