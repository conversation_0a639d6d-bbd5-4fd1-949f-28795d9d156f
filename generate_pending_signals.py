#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIFXHunter Pro v2.2 - Pending Signal Generator
Generate pending signals with various expiry times to test countdown timers
"""

import random
import time
from datetime import datetime, timedelta
from performance_database import PerformanceDatabase

def generate_pending_signals():
    """Generate pending signals with different expiry times for testing countdown timers."""
    print("⏳ Generating Pending Signals for Countdown Timer Testing")
    print("=" * 60)
    
    # Initialize database
    database = PerformanceDatabase()
    
    # Sample currency pairs
    currency_pairs = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "EURJPY", "GBPJPY"]
    
    # Sample strategies
    strategies = [
        "RSI_Oversold_Bounce",
        "MACD_Bullish_Crossover",
        "Multi_Indicator_Confluence",
        "Bollinger_Band_Breakout",
        "Stochastic_Oversold"
    ]
    
    # Sample market conditions
    market_conditions_options = [
        {"trend": "bullish", "volatility": "low", "momentum": "bullish"},
        {"trend": "bearish", "volatility": "medium", "momentum": "bearish"},
        {"trend": "bullish", "volatility": "high", "momentum": "bullish"},
        {"trend": "neutral", "volatility": "medium", "momentum": "neutral"}
    ]
    
    # Generate signals with different expiry times for testing
    signal_configs = [
        {"expiry_minutes": 1, "description": "1 minute (urgent test)"},
        {"expiry_minutes": 2, "description": "2 minutes (red zone test)"},
        {"expiry_minutes": 4, "description": "4 minutes (yellow zone test)"},
        {"expiry_minutes": 7, "description": "7 minutes (green zone test)"},
        {"expiry_minutes": 12, "description": "12 minutes (long term test)"}
    ]
    
    print(f"📊 Generating {len(signal_configs)} pending signals with different expiry times...")
    
    for i, config in enumerate(signal_configs):
        # Random signal parameters
        asset = random.choice(currency_pairs)
        strategy = random.choice(strategies)
        signal_type = random.choice(["CALL", "PUT"])
        confidence = random.randint(70, 95)
        
        # Generate realistic entry price based on asset
        if "JPY" in asset:
            entry_price = round(random.uniform(140, 155), 3)
        else:
            entry_price = round(random.uniform(1.0, 1.3), 5)
        
        # Random market conditions
        market_conditions = random.choice(market_conditions_options)
        
        # Generate signal timestamp (now)
        signal_time = datetime.now()
        
        # Calculate expiry time based on config
        expiry_time = signal_time + timedelta(minutes=config["expiry_minutes"])
        
        # Create signal data
        signal_data = {
            "signal_id": f"PENDING_{i+1:03d}_{int(time.time())}",
            "asset": asset,
            "signal": signal_type,
            "expiry": f"{config['expiry_minutes']}m",
            "confidence": f"{confidence}%",
            "entry_price": entry_price,
            "strategy_name": strategy,
            "strategy_text": f"Test strategy: {strategy}",
            "timeframe": "5m",
            "risk_level": random.choice(["Low", "Medium", "High"]),
            "market_conditions": market_conditions,
            "reasoning": [
                f"Test condition 1 for {strategy}",
                f"Test condition 2 for {strategy}",
                f"Market trend: {market_conditions['trend']}"
            ],
            "timestamp": signal_time.isoformat()
        }
        
        # Store signal
        success = database.store_signal(signal_data)
        if success:
            print(f"✅ Signal {i+1}: {asset} {signal_type} @ {entry_price} - {config['description']}")
            print(f"   📅 Created: {signal_time.strftime('%H:%M:%S')}")
            print(f"   ⏰ Expires: {expiry_time.strftime('%H:%M:%S')} ({config['expiry_minutes']}m from now)")
        else:
            print(f"❌ Failed to store signal {i+1}")
    
    # Show summary
    print(f"\n📋 Pending Signals Summary:")
    print(f"Total Pending Signals: {len(signal_configs)}")
    print(f"Expiry Range: 1-12 minutes")
    print(f"Test Coverage:")
    print(f"  🚨 Urgent (< 2 min): 2 signals")
    print(f"  ⚠️ Warning (2-5 min): 2 signals") 
    print(f"  ⏳ Normal (> 5 min): 1 signal")
    
    print(f"\n🎯 Countdown Timer Test Features:")
    print(f"  ✅ Real-time countdown updates (every second)")
    print(f"  ✅ Progress bar visualization")
    print(f"  ✅ Color-coded urgency levels")
    print(f"  ✅ Pulsing animation for urgent signals")
    print(f"  ✅ Expiry notifications")
    print(f"  ✅ Auto-refresh when signals expire")
    
    print(f"\n📊 Check the AIFXHunter dashboard to see:")
    print(f"  🟢 Green progress bars (> 5 minutes remaining)")
    print(f"  🟡 Yellow progress bars (2-5 minutes remaining)")
    print(f"  🔴 Red progress bars (< 2 minutes remaining)")
    print(f"  🚨 Pulsing animation (< 30 seconds remaining)")
    
    print(f"\n⏰ Timeline for Testing:")
    now = datetime.now()
    for i, config in enumerate(signal_configs):
        expiry = now + timedelta(minutes=config["expiry_minutes"])
        print(f"  Signal {i+1}: Expires at {expiry.strftime('%H:%M:%S')} ({config['expiry_minutes']}m)")
    
    print(f"\n🎉 Pending signal generation complete!")
    print(f"💡 Tip: Watch the dashboard for real-time countdown timers and progress bars!")
    
    return True

def generate_quick_test_signals():
    """Generate signals that expire very soon for immediate testing."""
    print("🚀 Generating Quick Test Signals (30s, 60s, 90s expiry)")
    print("=" * 50)
    
    database = PerformanceDatabase()
    
    quick_configs = [
        {"expiry_seconds": 30, "description": "30 seconds (immediate urgent test)"},
        {"expiry_seconds": 60, "description": "1 minute (quick red zone test)"},
        {"expiry_seconds": 90, "description": "90 seconds (quick yellow zone test)"}
    ]
    
    for i, config in enumerate(quick_configs):
        signal_time = datetime.now()
        expiry_time = signal_time + timedelta(seconds=config["expiry_seconds"])
        
        signal_data = {
            "signal_id": f"QUICK_{i+1:03d}_{int(time.time())}",
            "asset": "EURUSD",
            "signal": "CALL",
            "expiry": "1m",
            "confidence": "85%",
            "entry_price": 1.07912,
            "strategy_name": "Quick_Test_Strategy",
            "strategy_text": "Quick test for countdown timers",
            "timeframe": "1m",
            "risk_level": "Low",
            "market_conditions": {"trend": "bullish", "volatility": "low", "momentum": "bullish"},
            "reasoning": ["Quick test condition"],
            "timestamp": signal_time.isoformat()
        }
        
        success = database.store_signal(signal_data)
        if success:
            print(f"✅ Quick Signal {i+1}: EURUSD CALL - {config['description']}")
            print(f"   ⏰ Expires: {expiry_time.strftime('%H:%M:%S')}")
    
    print(f"\n🎯 Quick test signals created! Watch for:")
    print(f"  🚨 Urgent pulsing animation in 30 seconds")
    print(f"  🔴 Red progress bars and countdown timers")
    print(f"  📱 Expiry notifications")
    
    return True

if __name__ == "__main__":
    import sys
    
    try:
        if len(sys.argv) > 1 and sys.argv[1] == "quick":
            success = generate_quick_test_signals()
        else:
            success = generate_pending_signals()
            
        if success:
            print("\n🚀 Pending signals generated successfully!")
            print("💡 Tip: Refresh the AIFXHunter dashboard to see the countdown timers!")
            print("⏰ Watch the progress bars fill up and colors change as expiry approaches!")
        else:
            print("\n❌ Pending signal generation failed")
    except Exception as e:
        print(f"\n💥 Error generating pending signals: {e}")
        import traceback
        traceback.print_exc()
