# -*- coding: utf-8 -*-
# =============================================================================
# AIFXHunter Pro v2.2 - Strategy Engine
# AI-Powered Strategy Parser and Evaluator for Binary Options Trading
# =============================================================================

import re
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import pandas as pd
import numpy as np

class StrategyEngine:
    """
    AI-powered strategy engine that parses natural language trading strategies
    and evaluates them against real-time market data.
    """
    
    def __init__(self):
        self.supported_indicators = {
            'rsi': {'aliases': ['rsi', 'relative strength index'], 'params': ['period']},
            'macd': {'aliases': ['macd', 'moving average convergence divergence'], 'params': ['fast', 'slow', 'signal']},
            'ema': {'aliases': ['ema', 'exponential moving average'], 'params': ['period']},
            'sma': {'aliases': ['sma', 'simple moving average'], 'params': ['period']},
            'bb': {'aliases': ['bollinger bands', 'bb', 'bollinger'], 'params': ['period', 'std']},
            'stoch': {'aliases': ['stochastic', 'stoch', 'stochastic oscillator'], 'params': ['k_period', 'd_period']},
            'williams_r': {'aliases': ['williams %r', 'williams r', 'wr'], 'params': ['period']},
            'cci': {'aliases': ['cci', 'commodity channel index'], 'params': ['period']},
            'atr': {'aliases': ['atr', 'average true range'], 'params': ['period']},
            'adx': {'aliases': ['adx', 'average directional index'], 'params': ['period']}
        }
        
        self.comparison_operators = {
            'above': '>', 'below': '<', 'greater than': '>', 'less than': '<',
            'over': '>', 'under': '<', 'higher than': '>', 'lower than': '<',
            '>=': '>=', '<=': '<=', '=': '==', 'equals': '==', 'equal to': '=='
        }
        
        self.logical_operators = {
            'and': '&', 'or': '|', 'not': '~'
        }
        
        self.signal_actions = {
            'buy': 'CALL', 'call': 'CALL', 'long': 'CALL', 'bullish': 'CALL',
            'sell': 'PUT', 'put': 'PUT', 'short': 'PUT', 'bearish': 'PUT'
        }

    def parse_strategy(self, strategy_text: str) -> Dict[str, Any]:
        """
        Parse natural language strategy into structured format.
        
        Example: "If RSI is below 30 and MACD shows bullish crossover, then generate BUY signal"
        """
        try:
            strategy_text = strategy_text.lower().strip()

            # Extract conditions and actions
            conditions = self._extract_conditions(strategy_text)
            actions = self._extract_actions(strategy_text)

            # Parse timeframe if specified
            timeframe = self._extract_timeframe(strategy_text)

            # Extract expiry time if specified
            expiry = self._extract_expiry(strategy_text)

            parsed_strategy = {
                'original_text': strategy_text,
                'conditions': conditions,
                'actions': actions,
                'timeframe': timeframe or '5m',
                'expiry': expiry or 'auto',
                'valid': len(conditions) > 0 and len(actions) > 0,
                'parsed_at': datetime.now().isoformat()
            }

            return parsed_strategy
            
        except Exception as e:
            return {
                'original_text': strategy_text,
                'error': str(e),
                'valid': False,
                'parsed_at': datetime.now().isoformat()
            }

    def _extract_conditions(self, text: str) -> List[Dict[str, Any]]:
        """Extract trading conditions from strategy text."""
        conditions = []
        
        # Pattern for indicator conditions (e.g., "RSI below 30", "MACD above signal line")
        indicator_patterns = [
            r'(rsi|relative strength index)\s*(is\s*)?(above|below|over|under|greater than|less than|>=|<=|=|equals|equal to)\s*(\d+(?:\.\d+)?)',
            r'(macd|moving average convergence divergence)\s*(is\s*)?(above|below|over|under|greater than|less than)\s*(signal|signal line|zero|0)',
            r'(ema|exponential moving average)\s*\((\d+)\)\s*(is\s*)?(above|below|over|under|greater than|less than)\s*(price|close)',
            r'(sma|simple moving average)\s*\((\d+)\)\s*(is\s*)?(above|below|over|under|greater than|less than)\s*(price|close)',
            r'(bollinger bands|bb|bollinger)\s*(upper|lower|middle)?\s*(is\s*)?(above|below|over|under|greater than|less than)\s*(price|close)',
            r'(stochastic|stoch)\s*(is\s*)?(above|below|over|under|greater than|less than|>=|<=|=)\s*(\d+(?:\.\d+)?)',
            r'(williams %r|williams r|wr)\s*(is\s*)?(above|below|over|under|greater than|less than|>=|<=|=)\s*(-?\d+(?:\.\d+)?)',
            r'(cci|commodity channel index)\s*(is\s*)?(above|below|over|under|greater than|less than|>=|<=|=)\s*(-?\d+(?:\.\d+)?)'
        ]
        
        for pattern in indicator_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                groups = match.groups()
                condition = self._parse_condition_match(groups)
                if condition:
                    conditions.append(condition)
        
        # Pattern for crossover conditions
        crossover_patterns = [
            r'(macd|moving average convergence divergence)\s*(bullish|bearish)?\s*(crossover|cross over|crosses over|crosses above|crosses below)',
            r'(ema|exponential moving average)\s*\((\d+)\)\s*(crosses above|crosses below|crossover|cross over)\s*(ema|exponential moving average)\s*\((\d+)\)',
            r'(sma|simple moving average)\s*\((\d+)\)\s*(crosses above|crosses below|crossover|cross over)\s*(sma|simple moving average)\s*\((\d+)\)'
        ]
        
        for pattern in crossover_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                groups = match.groups()
                condition = self._parse_crossover_condition(groups)
                if condition:
                    conditions.append(condition)
        
        return conditions

    def _parse_condition_match(self, groups: Tuple) -> Optional[Dict[str, Any]]:
        """Parse a condition match into structured format."""
        try:
            indicator = groups[0].lower()
            operator = groups[2] if len(groups) > 2 else groups[1]
            value = groups[3] if len(groups) > 3 else groups[2]
            
            # Map indicator aliases to standard names
            standard_indicator = None
            for std_name, info in self.supported_indicators.items():
                if indicator in info['aliases']:
                    standard_indicator = std_name
                    break
            
            if not standard_indicator:
                return None
            
            # Map operator to standard format
            standard_operator = self.comparison_operators.get(operator.lower(), operator)
            
            return {
                'type': 'comparison',
                'indicator': standard_indicator,
                'operator': standard_operator,
                'value': float(value) if value.replace('.', '').replace('-', '').isdigit() else value,
                'raw_text': ' '.join(str(g) for g in groups if g)
            }
            
        except Exception:
            return None

    def _parse_crossover_condition(self, groups: Tuple) -> Optional[Dict[str, Any]]:
        """Parse crossover conditions."""
        try:
            indicator = groups[0].lower()
            direction = 'bullish' if 'bullish' in groups or 'above' in groups else 'bearish'
            
            return {
                'type': 'crossover',
                'indicator': indicator,
                'direction': direction,
                'raw_text': ' '.join(str(g) for g in groups if g)
            }
            
        except Exception:
            return None

    def _extract_actions(self, text: str) -> List[Dict[str, Any]]:
        """Extract trading actions from strategy text."""
        actions = []
        
        # Pattern for signal generation
        action_patterns = [
            r'(generate|create|signal|trigger)\s*(buy|call|long|bullish|sell|put|short|bearish)\s*(signal|trade|order)?',
            r'(buy|call|long|bullish|sell|put|short|bearish)\s*(signal|trade|order|position)?',
            r'then\s*(buy|call|long|bullish|sell|put|short|bearish)'
        ]
        
        for pattern in action_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                groups = match.groups()
                action_word = None
                for group in groups:
                    if group and group.lower() in self.signal_actions:
                        action_word = group.lower()
                        break
                
                if action_word:
                    actions.append({
                        'type': 'signal',
                        'action': self.signal_actions[action_word],
                        'raw_text': match.group(0)
                    })
        
        return actions

    def _extract_timeframe(self, text: str) -> Optional[str]:
        """Extract timeframe from strategy text."""
        timeframe_patterns = [
            r'(\d+)\s*(minute|min|m)\s*(timeframe|chart|period)',
            r'(1m|5m|15m|30m|1h|4h|1d)',
            r'(\d+)\s*(minute|min|m)'
        ]
        
        for pattern in timeframe_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                groups = match.groups()
                if len(groups) >= 2 and groups[0].isdigit():
                    return f"{groups[0]}m"
                elif groups[0] in ['1m', '5m', '15m', '30m', '1h', '4h', '1d']:
                    return groups[0]
        
        return None

    def _extract_expiry(self, text: str) -> Optional[str]:
        """Extract expiry time from strategy text."""
        expiry_patterns = [
            r'expiry\s*(\d+)\s*(minute|min|m)',
            r'expire\s*in\s*(\d+)\s*(minute|min|m)',
            r'(\d+)\s*(minute|min|m)\s*expiry'
        ]
        
        for pattern in expiry_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                groups = match.groups()
                if groups[0].isdigit():
                    return f"{groups[0]}m"
        
        return None

    def evaluate_strategy(self, strategy: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate a parsed strategy against current market data.
        
        Args:
            strategy: Parsed strategy from parse_strategy()
            market_data: Current market indicators and prices
            
        Returns:
            Evaluation result with signal recommendation
        """
        if not strategy.get('valid', False):
            return {
                'signal': None,
                'confidence': 0,
                'reasoning': ['Strategy is invalid'],
                'evaluation_time': datetime.now().isoformat()
            }
        
        try:
            conditions_met = []
            reasoning = []
            
            # Evaluate each condition
            for condition in strategy['conditions']:
                result = self._evaluate_condition(condition, market_data)
                conditions_met.append(result['met'])
                reasoning.append(result['reason'])
            
            # Determine if strategy triggers
            all_conditions_met = all(conditions_met) if conditions_met else False
            
            # Calculate confidence based on how many conditions are met
            confidence = (sum(conditions_met) / len(conditions_met) * 100) if conditions_met else 0
            
            # Generate signal if conditions are met
            signal = None
            if all_conditions_met and strategy['actions']:
                signal = {
                    'action': strategy['actions'][0]['action'],
                    'timeframe': strategy['timeframe'],
                    'expiry': strategy['expiry'],
                    'confidence': f"{confidence:.0f}%",
                    'strategy_name': self._generate_strategy_name(strategy)
                }
            
            return {
                'signal': signal,
                'confidence': confidence,
                'reasoning': reasoning,
                'conditions_met': conditions_met,
                'all_conditions_met': all_conditions_met,
                'evaluation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'signal': None,
                'confidence': 0,
                'reasoning': [f'Evaluation error: {str(e)}'],
                'evaluation_time': datetime.now().isoformat()
            }

    def _evaluate_condition(self, condition: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate a single condition against market data."""
        try:
            if condition['type'] == 'comparison':
                return self._evaluate_comparison_condition(condition, market_data)
            elif condition['type'] == 'crossover':
                return self._evaluate_crossover_condition(condition, market_data)
            else:
                return {'met': False, 'reason': f"Unknown condition type: {condition['type']}"}
                
        except Exception as e:
            return {'met': False, 'reason': f"Error evaluating condition: {str(e)}"}

    def _evaluate_comparison_condition(self, condition: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate comparison conditions (e.g., RSI < 30)."""
        indicator = condition['indicator']
        operator = condition['operator']
        target_value = condition['value']
        
        # Get current indicator value from market data
        current_value = market_data.get(indicator)
        
        if current_value is None:
            return {'met': False, 'reason': f"{indicator.upper()} data not available"}
        
        # Perform comparison
        try:
            if operator == '>':
                met = current_value > target_value
            elif operator == '<':
                met = current_value < target_value
            elif operator == '>=':
                met = current_value >= target_value
            elif operator == '<=':
                met = current_value <= target_value
            elif operator == '==':
                met = abs(current_value - target_value) < 0.001  # Float comparison tolerance
            else:
                return {'met': False, 'reason': f"Unknown operator: {operator}"}
            
            reason = f"{indicator.upper()}({current_value:.2f}) {operator} {target_value} → {'✓' if met else '✗'}"
            return {'met': met, 'reason': reason}
            
        except Exception as e:
            return {'met': False, 'reason': f"Comparison error: {str(e)}"}

    def _evaluate_crossover_condition(self, condition: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate crossover conditions (e.g., MACD bullish crossover)."""
        indicator = condition['indicator']
        direction = condition['direction']
        
        if indicator == 'macd':
            macd_line = market_data.get('macd')
            signal_line = market_data.get('macd_signal')
            
            if macd_line is None or signal_line is None:
                return {'met': False, 'reason': "MACD data not available"}
            
            # Simple crossover detection (would need historical data for proper implementation)
            if direction == 'bullish':
                met = macd_line > signal_line
                reason = f"MACD({macd_line:.4f}) {'>' if met else '<='} Signal({signal_line:.4f}) → {'Bullish ✓' if met else 'Not bullish ✗'}"
            else:
                met = macd_line < signal_line
                reason = f"MACD({macd_line:.4f}) {'<' if met else '>='} Signal({signal_line:.4f}) → {'Bearish ✓' if met else 'Not bearish ✗'}"
            
            return {'met': met, 'reason': reason}
        
        return {'met': False, 'reason': f"Crossover evaluation not implemented for {indicator}"}

    def _generate_strategy_name(self, strategy: Dict[str, Any]) -> str:
        """Generate a readable name for the strategy."""
        indicators = set()
        for condition in strategy['conditions']:
            indicators.add(condition['indicator'].upper())
        
        actions = set()
        for action in strategy['actions']:
            actions.add(action['action'])
        
        indicator_str = '_'.join(sorted(indicators))
        action_str = '_'.join(sorted(actions))
        
        return f"{indicator_str}_{action_str}_Strategy"

    def validate_strategy(self, strategy_text: str) -> Dict[str, Any]:
        """
        Validate a strategy without evaluating it.
        
        Returns validation result with suggestions for improvement.
        """
        parsed = self.parse_strategy(strategy_text)
        
        validation = {
            'valid': parsed.get('valid', False),
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        if not parsed.get('conditions'):
            validation['errors'].append("No trading conditions found")
            validation['suggestions'].append("Add conditions like 'RSI below 30' or 'MACD bullish crossover'")
        
        if not parsed.get('actions'):
            validation['errors'].append("No trading actions found")
            validation['suggestions'].append("Add actions like 'generate BUY signal' or 'create CALL signal'")
        
        # Check for supported indicators
        unsupported_indicators = []
        for condition in parsed.get('conditions', []):
            if condition.get('indicator') not in self.supported_indicators:
                unsupported_indicators.append(condition.get('indicator', 'unknown'))
        
        if unsupported_indicators:
            validation['warnings'].append(f"Unsupported indicators: {', '.join(unsupported_indicators)}")
        
        validation['valid'] = len(validation['errors']) == 0
        
        return validation
