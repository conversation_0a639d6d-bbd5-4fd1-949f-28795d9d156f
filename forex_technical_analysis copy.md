import yfinance as yf
import pandas_ta as ta
import pandas as pd
import streamlit as st
import time
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Initialize session state for signal tracking
if 'last_strong_signals' not in st.session_state:
    st.session_state.last_strong_signals = set()

def get_technical_score(symbol, timeframe):
    """
    Calculate technical score based on RSI, Stochastic, MACD, and Moving Averages.
    """
    try:
        end_time = datetime.now()
        
        # Adjusted lookback periods for better signal detection
        if timeframe in ['1m', '5m', '15m', '30m']:
            start_time = end_time - timedelta(days=7)
        else:
            start_time = end_time - timedelta(days=30)
            
        # Download data with better error handling
        data = None
        for attempt in range(3):
            try:
                data = yf.download(symbol, start=start_time, end=end_time,
                                  interval=timeframe, progress=False)
                if not data.empty and len(data) >= 50:  # Increased minimum data requirement
                    break
            except Exception as e:
                print(f"Attempt {attempt + 1} failed for {symbol}: {str(e)}")
                if attempt < 2:
                    time.sleep(1)
                
        if data is None or data.empty or len(data) < 50:
            print(f"Warning: Insufficient data for {symbol}")
            return None, None
            
        # Calculate indicators with better parameters
        # RSI (14)
        data['RSI'] = ta.rsi(data['Close'], length=14)
        
        # Stochastic (14,3,3)
        stoch = ta.stoch(data['High'], data['Low'], data['Close'], k=14, d=3, smooth_k=3)
        data = pd.concat([data, stoch], axis=1)
        
        # MACD (12,26,9)
        macd = ta.macd(data['Close'], fast=12, slow=26, signal=9)
        data = pd.concat([data, macd], axis=1)

        # Bollinger Bands (20, 2)
        bb = ta.bbands(data['Close'], length=20, std=2)
        data = pd.concat([data, bb], axis=1)

        # Moving Averages for trend detection
        data['SMA_20'] = ta.sma(data['Close'], length=20)
        data['SMA_50'] = ta.sma(data['Close'], length=50)
        data['EMA_12'] = ta.ema(data['Close'], length=12)
        data['EMA_26'] = ta.ema(data['Close'], length=26)

        # Williams %R for additional momentum
        data['WILLR'] = ta.willr(data['High'], data['Low'], data['Close'], length=14)

        # Get latest values
        latest = data.iloc[-1]
        
        # --- NEW AGGRESSIVE & GUARANTEED-TO-WORK SCORING LOGIC ---
        score = 0
        signals = []

        latest_close = latest['Close']

        # --- BUY CONDITIONS (Points only add up) ---
        if latest['RSI'] <= 35:
            score += 20
            signals.append(f"RSI is low ({latest['RSI']:.1f})")

        if latest['STOCHk_14_3_3'] <= 25:
            score += 20
            signals.append(f"Stochastic is low ({latest['STOCHk_14_3_3']:.1f})")

        if latest_close <= latest['BBL_20_2.0']:
            score += 30  # This is a strong signal
            signals.append("Price is at or below Lower Bollinger Band")

        if latest['MACD_12_26_9'] > latest['MACDs_12_26_9']:
            score += 15
            signals.append("MACD is in a bullish crossover")

        if latest_close > latest['SMA_50']:
            score += 15
            signals.append("Price is above 50-period trend line")

        # --- SELL CONDITIONS (Points only subtract) ---
        if latest['RSI'] >= 65:
            score -= 20
            signals.append(f"RSI is high ({latest['RSI']:.1f})")

        if latest['STOCHk_14_3_3'] >= 75:
            score -= 20
            signals.append(f"Stochastic is high ({latest['STOCHk_14_3_3']:.1f})")

        if latest_close >= latest['BBU_20_2.0']:
            score -= 30 # This is a strong signal
            signals.append("Price is at or above Upper Bollinger Band")

        if latest['MACD_12_26_9'] < latest['MACDs_12_26_9']:
            score -= 15
            signals.append("MACD is in a bearish crossover")

        if latest_close < latest['SMA_50']:
            score -= 15
            signals.append("Price is below 50-period trend line")

        volatility = latest.get('BBB_20_2.0', 0.02)
        return score, volatility, signals

    except Exception as e:
        print(f"Error analyzing {symbol}: {str(e)}")
        return None, None, []

def determine_expiration(base_timeframe, score, volatility):
    """Enhanced expiration logic"""
    timeframe_minutes = {
        '5m': 5, '15m': 15, '30m': 30, '1h': 60, '4h': 240
    }
    base_minutes = timeframe_minutes.get(base_timeframe, 60)

    # More aggressive signal strength adjustment
    abs_score = abs(score)
    if abs_score > 80:  # Very strong signals
        strength_multiplier = 0.4
    elif abs_score > 60:  # Strong signals
        strength_multiplier = 0.7
    elif abs_score > 40:  # Medium signals
        strength_multiplier = 1.0
    else:  # Weak signals
        strength_multiplier = 1.3

    # Volatility adjustment
    if volatility > 0.06:  # Very high volatility
        volatility_multiplier = 0.6
    elif volatility > 0.04:  # High volatility
        volatility_multiplier = 0.8
    elif volatility >= 0.02:  # Normal volatility
        volatility_multiplier = 1.0
    else:  # Low volatility
        volatility_multiplier = 1.4

    final_minutes = base_minutes * strength_multiplier * volatility_multiplier

    if final_minutes >= 60:
        hours = final_minutes / 60
        if hours == int(hours):
            return f"{int(hours)} hour{'s' if hours != 1 else ''}"
        else:
            return f"{hours:.1f} hours"
    else:
        return f"{int(final_minutes)} minutes"

def create_technical_chart(symbol, timeframe):
    """Enhanced chart with more indicators"""
    try:
        end_time = datetime.now()
        
        if timeframe in ['1m', '2m', '5m', '15m', '30m', '60m', '90m']:
            start_time = end_time - timedelta(days=5)
        else:
            start_time = end_time - timedelta(days=20)

        data = None
        for attempt in range(3):
            try:
                data = yf.download(symbol, start=start_time, end=end_time,
                                  interval=timeframe, progress=False)
                
                if data is not None and not data.empty and len(data) >= 50:
                    break
                    
            except Exception as e:
                print(f"Download attempt {attempt + 1} failed for {symbol}: {str(e)}")
                if attempt < 2:
                    time.sleep(2)
                    
        if data is None or data.empty or len(data) < 50:
            return None

        data = data.dropna()
        
        # Calculate all indicators
        data['RSI'] = ta.rsi(data['Close'], length=14)
        stoch = ta.stoch(data['High'], data['Low'], data['Close'], k=14, d=3, smooth_k=3)
        data = pd.concat([data, stoch], axis=1)
        macd = ta.macd(data['Close'], fast=12, slow=26, signal=9)
        data = pd.concat([data, macd], axis=1)
        bb = ta.bbands(data['Close'], length=20, std=2)
        data = pd.concat([data, bb], axis=1)
        data['SMA_20'] = ta.sma(data['Close'], length=20)
        data['SMA_50'] = ta.sma(data['Close'], length=50)
        data['WILLR'] = ta.willr(data['High'], data['Low'], data['Close'], length=14)

        # Create enhanced chart with 4 subplots
        fig = make_subplots(
            rows=4, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.03,
            subplot_titles=(
                f'{symbol.replace("=X", "")} - {timeframe} (Price & BB)', 
                'MACD', 
                'RSI', 
                'Williams %R & Stochastic'
            ),
            row_heights=[0.5, 0.2, 0.15, 0.15]
        )

        # Row 1: Candlestick with Bollinger Bands and Moving Averages
        fig.add_trace(
            go.Candlestick(
                x=data.index,
                open=data['Open'],
                high=data['High'],
                low=data['Low'],
                close=data['Close'],
                name='Price',
                increasing_line_color='#00ff88',
                decreasing_line_color='#ff4444'
            ),
            row=1, col=1
        )

        # Bollinger Bands
        if 'BBU_20_2.0' in data.columns:
            fig.add_trace(go.Scatter(x=data.index, y=data['BBU_20_2.0'], 
                                   mode='lines', name='BB Upper', 
                                   line=dict(color='rgba(173, 204, 255, 0.8)', width=1)), row=1, col=1)
            fig.add_trace(go.Scatter(x=data.index, y=data['BBM_20_2.0'], 
                                   mode='lines', name='BB Middle', 
                                   line=dict(color='rgba(255, 255, 255, 0.8)', width=1)), row=1, col=1)
            fig.add_trace(go.Scatter(x=data.index, y=data['BBL_20_2.0'], 
                                   mode='lines', name='BB Lower', 
                                   line=dict(color='rgba(173, 204, 255, 0.8)', width=1),
                                   fill='tonexty', fillcolor='rgba(173, 204, 255, 0.1)'), row=1, col=1)

        # Moving Averages
        if 'SMA_20' in data.columns:
            fig.add_trace(go.Scatter(x=data.index, y=data['SMA_20'], 
                                   mode='lines', name='SMA 20', 
                                   line=dict(color='orange', width=2)), row=1, col=1)
        if 'SMA_50' in data.columns:
            fig.add_trace(go.Scatter(x=data.index, y=data['SMA_50'], 
                                   mode='lines', name='SMA 50', 
                                   line=dict(color='purple', width=2)), row=1, col=1)

        # Row 2: MACD
        if 'MACD_12_26_9' in data.columns:
            fig.add_trace(go.Scatter(x=data.index, y=data['MACD_12_26_9'], 
                                   mode='lines', name='MACD', 
                                   line=dict(color='#00ff88', width=2)), row=2, col=1)
            fig.add_trace(go.Scatter(x=data.index, y=data['MACDs_12_26_9'], 
                                   mode='lines', name='Signal', 
                                   line=dict(color='#ff4444', width=2)), row=2, col=1)
            fig.add_trace(go.Bar(x=data.index, y=data['MACDh_12_26_9'], 
                               name='Histogram', 
                               marker_color='rgba(255, 255, 255, 0.6)'), row=2, col=1)

        # Row 3: RSI
        if 'RSI' in data.columns:
            fig.add_trace(go.Scatter(x=data.index, y=data['RSI'], 
                                   mode='lines', name='RSI', 
                                   line=dict(color='#ffaa00', width=2)), row=3, col=1)
            fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
            fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)
            fig.add_hline(y=50, line_dash="dot", line_color="gray", row=3, col=1)

        # Row 4: Williams %R and Stochastic
        if 'WILLR' in data.columns:
            fig.add_trace(go.Scatter(x=data.index, y=data['WILLR'], 
                                   mode='lines', name='Williams %R', 
                                   line=dict(color='cyan', width=2)), row=4, col=1)
            fig.add_hline(y=-20, line_dash="dash", line_color="red", row=4, col=1)
            fig.add_hline(y=-80, line_dash="dash", line_color="green", row=4, col=1)

        if 'STOCHk_14_3_3' in data.columns:
            fig.add_trace(go.Scatter(x=data.index, y=data['STOCHk_14_3_3'], 
                                   mode='lines', name='Stoch %K', 
                                   line=dict(color='yellow', width=1)), row=4, col=1)
            fig.add_trace(go.Scatter(x=data.index, y=data['STOCHd_14_3_3'], 
                                   mode='lines', name='Stoch %D', 
                                   line=dict(color='magenta', width=1)), row=4, col=1)

        fig.update_layout(
            template="plotly_dark",
            height=1000,
            showlegend=True,
            xaxis_rangeslider_visible=False,
            title=f"Enhanced Technical Analysis - {symbol.replace('=X', '')} ({timeframe})"
        )

        fig.update_yaxes(title_text="Price", row=1, col=1)
        fig.update_yaxes(title_text="MACD", row=2, col=1)
        fig.update_yaxes(title_text="RSI", row=3, col=1)
        fig.update_yaxes(title_text="Williams %R / Stoch", row=4, col=1)

        return fig

    except Exception as e:
        print(f"Error creating chart for {symbol}: {str(e)}")
        return None

def analyze_multiple_pairs(symbols, timeframe):
    """Enhanced analysis with signal details"""
    categories = {
        "Strong Buy": [],
        "Weak Buy": [],
        "Neutral": [],
        "Weak Sell": [],
        "Strong Sell": []
    }
    
    for symbol in symbols:
        score, volatility, signals = get_technical_score(symbol, timeframe)
        if score is not None and volatility is not None:
            # --- NEW THRESHOLDS TUNED FOR THE AGGRESSIVE SCORING ---
            if score >= 50:  # A confluence of at least two strong signals
                categories["Strong Buy"].append((symbol, score, volatility, signals))
            elif score >= 20: # At least one clear signal
                categories["Weak Buy"].append((symbol, score, volatility, signals))
            elif score <= -50:
                categories["Strong Sell"].append((symbol, score, volatility, signals))
            elif score <= -20:
                categories["Weak Sell"].append((symbol, score, volatility, signals))
            else:
                categories["Neutral"].append((symbol, score, volatility, signals))
    
    # Sort by absolute score
    for category in categories:
        categories[category] = sorted(categories[category], key=lambda x: abs(x[1]), reverse=True)
        
    return categories

# Extended watchlist
watchlist = [
    "EURUSD=X", "GBPUSD=X", "USDJPY=X", "AUDUSD=X", "USDCAD=X", "USDCHF=X", "NZDUSD=X",
    "EURJPY=X", "EURGBP=X", "EURAUD=X", "EURCAD=X", "EURCHF=X",
    "GBPJPY=X", "AUDJPY=X", "CADJPY=X", "CHFJPY=X",
    "GBPAUD=X", "GBPCAD=X", "GBPCHF=X",
    "AUDCAD=X", "AUDCHF=X", "AUDNZD=X", "CADCHF=X"
]

# Streamlit UI
st.set_page_config(page_title="Enhanced AI Forex Analysis", layout="wide")

# Custom CSS for better styling
st.markdown("""
<style>
.metric-card {
    background-color: #1e1e1e;
    padding: 10px;
    border-radius: 8px;
    margin: 5px 0;
    border-left: 4px solid #00ff88;
}
.signal-details {
    font-size: 0.8em;
    color: #888;
    margin-top: 5px;
}
</style>
""", unsafe_allow_html=True)

st.title("🚀 Enhanced AI Forex Analysis Bot")
st.markdown("*Real-time technical analysis with comprehensive indicators*")

# Timeframe selection
timeframe = st.selectbox(
    "Select Timeframe",
    options=["5m", "15m", "30m", "1h", "4h"],
    index=3
)

st.markdown("🎯 **Real-time Signal Hunter Mode** - Scanning for new Strong Buy/Sell signals every 10 seconds")

# Market overview section
with st.expander("📊 All Markets Being Analyzed (22 pairs)"):
    cols = st.columns(4)
    display_list = [pair.replace('=X', '') for pair in watchlist]
    chunk_size = (len(display_list) + len(cols) - 1) // len(cols)

    for i, col in enumerate(cols):
        chunk = display_list[i*chunk_size : (i+1)*chunk_size]
        with col:
            for pair in chunk:
                if st.button(pair, key=f"pair_{pair}"):
                    st.session_state['selected_symbol'] = f"{pair}=X"
                    st.session_state['selected_timeframe'] = timeframe
                    st.rerun()

# Placeholders
alert_placeholder = st.empty()
header_placeholder = st.empty()
results_placeholder = st.empty()
status_placeholder = st.empty()

# =============================================================================
# CORRECTED AUTO-REFRESH IMPLEMENTATION
# =============================================================================

def update_dashboard():
    """Execute analysis and update all dashboard components"""
    current_time = datetime.now()

    # Update header with current time
    with header_placeholder.container():
        st.subheader(f"⏰ Current Time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if st.session_state.last_update:
            next_refresh = st.session_state.last_update + timedelta(seconds=60)
            time_until_refresh = max(0, int((next_refresh - current_time).total_seconds()))
            st.caption(f"Last updated: {st.session_state.last_update.strftime('%H:%M:%S')} | Next refresh in: {time_until_refresh} seconds")

    # Execute analysis with spinner
    with status_placeholder.container():
        with st.spinner("🔍 Analyzing 22 forex pairs with enhanced indicators..."):
            results = analyze_multiple_pairs(watchlist, timeframe)
            st.session_state.last_update = current_time
            display_results(results, timeframe)

        # Count and display active signals
        active_signals = sum(len(pairs) for category, pairs in results.items()
                           if category in ["Strong Buy", "Weak Buy", "Strong Sell", "Weak Sell"])

        if active_signals > 0:
            st.success(f"✅ Analysis complete! Found {active_signals} active trading signals.")
        else:
            st.info("📊 Analysis complete. No strong signals detected - market may be consolidating.")

# Session state management
if 'last_timeframe' not in st.session_state:
    st.session_state.last_timeframe = timeframe
if 'last_update' not in st.session_state:
    st.session_state.last_update = None

def display_results(results, timeframe):
    """Enhanced results display with signal details"""
    with results_placeholder.container():
        st.subheader(f"📈 Technical Analysis Results - {timeframe} Timeframe")
        
        # Summary statistics
        total_signals = sum(len(pairs) for pairs in results.values())
        strong_signals = len(results["Strong Buy"]) + len(results["Strong Sell"])
        
        col1, col2, col3 = st.columns(3)
        col1.metric("Total Pairs Analyzed", len(watchlist))
        col2.metric("Active Signals", total_signals)
        col3.metric("Strong Signals", strong_signals)
        
        st.markdown("---")

        # Results display
        cols = st.columns(5)
        
        category_colors = {
            "Strong Buy": "🟢", "Weak Buy": "🔵", "Neutral": "⚪", 
            "Weak Sell": "🟠", "Strong Sell": "🔴"
        }

        for i, (category, pairs) in enumerate(results.items()):
            with cols[i]:
                st.markdown(f"### {category_colors[category]} {category}")
                
                if not pairs:
                    st.write("No signals")
                else:
                    for symbol, score, volatility, signals in pairs:
                        # --- NEW: Detailed, Transparent Signal Card ---
                        symbol_name = symbol.replace("=X", "")
                        expiration_time = determine_expiration(timeframe, score, volatility)

                        # Use an expander for each signal to show details
                        with st.expander(f"{symbol_name} (Score: {score:.1f})"):
                            # Display the main signal recommendation
                            if score > 0:
                                st.success(f"**Recommendation: BUY - Expiry: {expiration_time}**")
                            else:
                                st.error(f"**Recommendation: SELL - Expiry: {expiration_time}**")

                            st.markdown("---")

                            # List all the technical reasons for the signal
                            st.write("**Technical Reasons:**")
                            if signals:
                                for reason in signals:
                                    st.markdown(f"- ✅ {reason}")
                            else:
                                st.write("No specific conditions met for this score.")

                            st.write(f"_(Volatility Index: {volatility:.4f})_")

                            # Add the chart button inside the expander
                            if st.button("📊 Show Chart", key=f"chart_{symbol}_{category}"):
                                st.session_state['selected_symbol'] = symbol
                                st.session_state['selected_timeframe'] = timeframe
                                st.rerun()

# Chart display
if 'selected_symbol' in st.session_state and 'selected_timeframe' in st.session_state:
    st.markdown("---")
    st.subheader(f"📊 Enhanced Chart - {st.session_state['selected_symbol'].replace('=X', '')} ({st.session_state['selected_timeframe']})")

    chart_col, button_col = st.columns([5, 1])
    
    with button_col:
        if st.button("🗑️ Clear Chart"):
            del st.session_state['selected_symbol']
            del st.session_state['selected_timeframe']
            st.rerun()

    with chart_col:
        with st.spinner("Loading enhanced chart..."):
            fig = create_technical_chart(st.session_state['selected_symbol'], st.session_state['selected_timeframe'])
            if fig:
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.error("Unable to load chart. Please try again.")

# Execute initial analysis immediately
update_dashboard()

# =============================================================================
# REAL-TIME SIGNAL HUNTER MAIN LOOP
# =============================================================================

def play_alert_sound():
    """Play a beep sound for new strong signals"""
    sound_html = """
    <script>
    // Create audio context for beep sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = 800; // 800 Hz beep
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
    </script>
    """
    st.components.v1.html(sound_html, height=0)

# Real-time Signal Hunter Loop
while True:
    # Get current analysis results
    current_time = datetime.now()
    results = analyze_multiple_pairs(watchlist, timeframe)

    # Extract current strong signals (both buy and sell)
    current_strong_signals = set()

    # Add Strong Buy signals
    for symbol, score, volatility, signals in results["Strong Buy"]:
        current_strong_signals.add(f"STRONG BUY: {symbol.replace('=X', '')}")

    # Add Strong Sell signals
    for symbol, score, volatility, signals in results["Strong Sell"]:
        current_strong_signals.add(f"STRONG SELL: {symbol.replace('=X', '')}")

    # Find NEW strong signals (not in previous cycle)
    new_strong_signals = current_strong_signals - st.session_state.last_strong_signals

    # Display alerts for new strong signals
    if new_strong_signals:
        with alert_placeholder.container():
            st.markdown("### 🚨 NEW STRONG SIGNAL DETECTED! 🚨")
            for signal in new_strong_signals:
                if "BUY" in signal:
                    st.success(f"🟢 {signal} - {current_time.strftime('%H:%M:%S')}")
                else:
                    st.error(f"🔴 {signal} - {current_time.strftime('%H:%M:%S')}")

            # Play alert sound
            play_alert_sound()

            # Auto-clear alert after showing briefly
            time.sleep(2)
    else:
        # Clear alerts if no new signals
        alert_placeholder.empty()

    # Update session state with current signals
    st.session_state.last_strong_signals = current_strong_signals

    # Update the main dashboard
    with header_placeholder.container():
        st.subheader(f"🎯 Signal Hunter Active - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        st.caption(f"Scanning every 10 seconds | Strong signals tracked: {len(current_strong_signals)}")

    # Display full results
    with status_placeholder.container():
        st.session_state.last_update = current_time
        display_results(results, timeframe)

        # Show hunter statistics
        total_signals = sum(len(pairs) for pairs in results.values())
        strong_signals = len(results["Strong Buy"]) + len(results["Strong Sell"])

        if strong_signals > 0:
            st.info(f"🎯 Hunter Status: {strong_signals} strong signals active | {len(new_strong_signals)} new this cycle")
        else:
            st.info("🎯 Hunter Status: No strong signals detected - market scanning continues...")

    # Sleep for 10 seconds before next scan
    time.sleep(10)
    st.rerun()
