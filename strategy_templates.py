# -*- coding: utf-8 -*-
# =============================================================================
# AIFXHunter Pro v2.2 - Strategy Templates
# Pre-defined trading strategy templates for binary options
# =============================================================================

from typing import Dict, List, Any
from datetime import datetime

class StrategyTemplates:
    """
    Collection of pre-defined trading strategy templates that users can
    select and customize for their binary options trading.
    """
    
    def __init__(self):
        self.templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize all strategy templates."""
        return {
            "rsi_oversold_bounce": {
                "name": "RSI Oversold Bounce",
                "description": "Buy when RSI drops below 30 (oversold condition)",
                "category": "Mean Reversion",
                "difficulty": "Beginner",
                "timeframe": "5m",
                "expiry": "5m",
                "strategy_text": "If RSI is below 30, then generate CALL signal",
                "conditions": [
                    {
                        "type": "comparison",
                        "indicator": "rsi",
                        "operator": "<",
                        "value": 30,
                        "description": "RSI below 30 (oversold)"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "CALL",
                        "description": "Generate CALL signal"
                    }
                ],
                "success_rate": "65-75%",
                "risk_level": "Medium",
                "best_pairs": ["EURUSD", "GBPUSD", "USDJPY"],
                "notes": "Works best in ranging markets. Avoid during strong trends."
            },
            
            "rsi_overbought_reversal": {
                "name": "RSI Overbought Reversal",
                "description": "Sell when RSI rises above 70 (overbought condition)",
                "category": "Mean Reversion",
                "difficulty": "Beginner",
                "timeframe": "5m",
                "expiry": "5m",
                "strategy_text": "If RSI is above 70, then generate PUT signal",
                "conditions": [
                    {
                        "type": "comparison",
                        "indicator": "rsi",
                        "operator": ">",
                        "value": 70,
                        "description": "RSI above 70 (overbought)"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "PUT",
                        "description": "Generate PUT signal"
                    }
                ],
                "success_rate": "65-75%",
                "risk_level": "Medium",
                "best_pairs": ["EURUSD", "GBPUSD", "USDJPY"],
                "notes": "Works best in ranging markets. Avoid during strong trends."
            },
            
            "macd_bullish_crossover": {
                "name": "MACD Bullish Crossover",
                "description": "Buy when MACD line crosses above signal line",
                "category": "Trend Following",
                "difficulty": "Intermediate",
                "timeframe": "5m",
                "expiry": "15m",
                "strategy_text": "If MACD shows bullish crossover, then generate CALL signal",
                "conditions": [
                    {
                        "type": "crossover",
                        "indicator": "macd",
                        "direction": "bullish",
                        "description": "MACD bullish crossover"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "CALL",
                        "description": "Generate CALL signal"
                    }
                ],
                "success_rate": "70-80%",
                "risk_level": "Medium",
                "best_pairs": ["EURUSD", "GBPUSD", "AUDUSD"],
                "notes": "Best during trending markets. Confirm with volume if available."
            },
            
            "macd_bearish_crossover": {
                "name": "MACD Bearish Crossover",
                "description": "Sell when MACD line crosses below signal line",
                "category": "Trend Following",
                "difficulty": "Intermediate",
                "timeframe": "5m",
                "expiry": "15m",
                "strategy_text": "If MACD shows bearish crossover, then generate PUT signal",
                "conditions": [
                    {
                        "type": "crossover",
                        "indicator": "macd",
                        "direction": "bearish",
                        "description": "MACD bearish crossover"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "PUT",
                        "description": "Generate PUT signal"
                    }
                ],
                "success_rate": "70-80%",
                "risk_level": "Medium",
                "best_pairs": ["EURUSD", "GBPUSD", "AUDUSD"],
                "notes": "Best during trending markets. Confirm with volume if available."
            },
            
            "bollinger_squeeze_breakout": {
                "name": "Bollinger Band Squeeze Breakout",
                "description": "Trade breakouts when price moves outside Bollinger Bands",
                "category": "Breakout",
                "difficulty": "Advanced",
                "timeframe": "5m",
                "expiry": "5m",
                "strategy_text": "If price is above Bollinger upper band, then generate CALL signal",
                "conditions": [
                    {
                        "type": "comparison",
                        "indicator": "price_vs_bb_upper",
                        "operator": ">",
                        "value": 0,
                        "description": "Price above Bollinger upper band"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "CALL",
                        "description": "Generate CALL signal"
                    }
                ],
                "success_rate": "60-70%",
                "risk_level": "High",
                "best_pairs": ["GBPJPY", "EURJPY", "AUDJPY"],
                "notes": "High volatility strategy. Use smaller position sizes."
            },
            
            "stochastic_oversold": {
                "name": "Stochastic Oversold",
                "description": "Buy when Stochastic %K is below 20",
                "category": "Oscillator",
                "difficulty": "Beginner",
                "timeframe": "5m",
                "expiry": "5m",
                "strategy_text": "If Stochastic is below 20, then generate CALL signal",
                "conditions": [
                    {
                        "type": "comparison",
                        "indicator": "stoch_k",
                        "operator": "<",
                        "value": 20,
                        "description": "Stochastic %K below 20"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "CALL",
                        "description": "Generate CALL signal"
                    }
                ],
                "success_rate": "60-70%",
                "risk_level": "Medium",
                "best_pairs": ["EURUSD", "USDCAD", "NZDUSD"],
                "notes": "Similar to RSI but more sensitive to price changes."
            },
            
            "multi_indicator_confluence": {
                "name": "Multi-Indicator Confluence",
                "description": "Strong signals when multiple indicators align",
                "category": "Confluence",
                "difficulty": "Advanced",
                "timeframe": "5m",
                "expiry": "15m",
                "strategy_text": "If RSI is below 30 and Stochastic is below 20 and MACD shows bullish crossover, then generate CALL signal",
                "conditions": [
                    {
                        "type": "comparison",
                        "indicator": "rsi",
                        "operator": "<",
                        "value": 30,
                        "description": "RSI oversold"
                    },
                    {
                        "type": "comparison",
                        "indicator": "stoch_k",
                        "operator": "<",
                        "value": 20,
                        "description": "Stochastic oversold"
                    },
                    {
                        "type": "crossover",
                        "indicator": "macd",
                        "direction": "bullish",
                        "description": "MACD bullish crossover"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "CALL",
                        "description": "Generate CALL signal"
                    }
                ],
                "success_rate": "80-90%",
                "risk_level": "Low",
                "best_pairs": ["EURUSD", "GBPUSD", "USDJPY"],
                "notes": "High accuracy but fewer signals. Wait for all conditions."
            },
            
            "williams_r_reversal": {
                "name": "Williams %R Reversal",
                "description": "Trade reversals using Williams %R oscillator",
                "category": "Oscillator",
                "difficulty": "Intermediate",
                "timeframe": "5m",
                "expiry": "5m",
                "strategy_text": "If Williams %R is below -80, then generate CALL signal",
                "conditions": [
                    {
                        "type": "comparison",
                        "indicator": "williams_r",
                        "operator": "<",
                        "value": -80,
                        "description": "Williams %R oversold"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "CALL",
                        "description": "Generate CALL signal"
                    }
                ],
                "success_rate": "65-75%",
                "risk_level": "Medium",
                "best_pairs": ["AUDUSD", "NZDUSD", "USDCAD"],
                "notes": "Good for volatile pairs. Similar to Stochastic but inverted."
            },
            
            "cci_extreme_levels": {
                "name": "CCI Extreme Levels",
                "description": "Trade when CCI reaches extreme overbought/oversold levels",
                "category": "Oscillator",
                "difficulty": "Intermediate",
                "timeframe": "5m",
                "expiry": "5m",
                "strategy_text": "If CCI is below -100, then generate CALL signal",
                "conditions": [
                    {
                        "type": "comparison",
                        "indicator": "cci",
                        "operator": "<",
                        "value": -100,
                        "description": "CCI oversold below -100"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "CALL",
                        "description": "Generate CALL signal"
                    }
                ],
                "success_rate": "70-80%",
                "risk_level": "Medium",
                "best_pairs": ["GBPUSD", "EURJPY", "GBPJPY"],
                "notes": "Wait for extreme levels. CCI can stay extreme longer than expected."
            },
            
            "trend_following_ema": {
                "name": "EMA Trend Following",
                "description": "Follow trends using EMA crossovers",
                "category": "Trend Following",
                "difficulty": "Beginner",
                "timeframe": "15m",
                "expiry": "15m",
                "strategy_text": "If EMA(21) crosses above EMA(50), then generate CALL signal",
                "conditions": [
                    {
                        "type": "crossover",
                        "indicator": "ema",
                        "direction": "bullish",
                        "description": "EMA(21) above EMA(50)"
                    }
                ],
                "actions": [
                    {
                        "type": "signal",
                        "action": "CALL",
                        "description": "Generate CALL signal"
                    }
                ],
                "success_rate": "75-85%",
                "risk_level": "Low",
                "best_pairs": ["EURUSD", "GBPUSD", "USDJPY"],
                "notes": "Best for trending markets. Use longer timeframes for better signals."
            }
        }
    
    def get_all_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get all available strategy templates."""
        return self.templates
    
    def get_template(self, template_id: str) -> Dict[str, Any]:
        """Get a specific template by ID."""
        return self.templates.get(template_id, {})
    
    def get_templates_by_category(self, category: str) -> Dict[str, Dict[str, Any]]:
        """Get templates filtered by category."""
        return {
            tid: template for tid, template in self.templates.items()
            if template.get('category', '').lower() == category.lower()
        }
    
    def get_templates_by_difficulty(self, difficulty: str) -> Dict[str, Dict[str, Any]]:
        """Get templates filtered by difficulty level."""
        return {
            tid: template for tid, template in self.templates.items()
            if template.get('difficulty', '').lower() == difficulty.lower()
        }
    
    def get_beginner_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get templates suitable for beginners."""
        return self.get_templates_by_difficulty('beginner')
    
    def get_categories(self) -> List[str]:
        """Get all available strategy categories."""
        categories = set()
        for template in self.templates.values():
            categories.add(template.get('category', 'Unknown'))
        return sorted(list(categories))
    
    def get_template_summary(self, template_id: str) -> str:
        """Get a formatted summary of a template."""
        template = self.get_template(template_id)
        if not template:
            return "Template not found"
        
        summary = f"""
**{template['name']}**
- **Category:** {template.get('category', 'Unknown')}
- **Difficulty:** {template.get('difficulty', 'Unknown')}
- **Success Rate:** {template.get('success_rate', 'Unknown')}
- **Risk Level:** {template.get('risk_level', 'Unknown')}
- **Best Timeframe:** {template.get('timeframe', '5m')}
- **Recommended Expiry:** {template.get('expiry', '5m')}

**Description:** {template.get('description', 'No description available')}

**Strategy:** {template.get('strategy_text', 'No strategy text available')}

**Best Currency Pairs:** {', '.join(template.get('best_pairs', []))}

**Notes:** {template.get('notes', 'No additional notes')}
        """
        
        return summary.strip()
    
    def search_templates(self, query: str) -> Dict[str, Dict[str, Any]]:
        """Search templates by name, description, or category."""
        query = query.lower()
        results = {}
        
        for tid, template in self.templates.items():
            searchable_text = ' '.join([
                template.get('name', ''),
                template.get('description', ''),
                template.get('category', ''),
                template.get('strategy_text', ''),
                ' '.join(template.get('best_pairs', []))
            ]).lower()
            
            if query in searchable_text:
                results[tid] = template
        
        return results
    
    def get_recommended_templates(self, user_level: str = "beginner") -> List[str]:
        """Get recommended template IDs based on user experience level."""
        recommendations = {
            'beginner': [
                'rsi_oversold_bounce',
                'rsi_overbought_reversal',
                'stochastic_oversold',
                'trend_following_ema'
            ],
            'intermediate': [
                'macd_bullish_crossover',
                'macd_bearish_crossover',
                'williams_r_reversal',
                'cci_extreme_levels'
            ],
            'advanced': [
                'bollinger_squeeze_breakout',
                'multi_indicator_confluence'
            ]
        }
        
        return recommendations.get(user_level.lower(), recommendations['beginner'])
    
    def create_custom_template(self, name: str, strategy_text: str, **kwargs) -> Dict[str, Any]:
        """Create a custom template from user input."""
        template = {
            'name': name,
            'description': kwargs.get('description', f'Custom strategy: {name}'),
            'category': kwargs.get('category', 'Custom'),
            'difficulty': kwargs.get('difficulty', 'Custom'),
            'timeframe': kwargs.get('timeframe', '5m'),
            'expiry': kwargs.get('expiry', '5m'),
            'strategy_text': strategy_text,
            'success_rate': kwargs.get('success_rate', 'Unknown'),
            'risk_level': kwargs.get('risk_level', 'Unknown'),
            'best_pairs': kwargs.get('best_pairs', []),
            'notes': kwargs.get('notes', 'Custom user-created strategy'),
            'created_at': datetime.now().isoformat(),
            'custom': True
        }
        
        return template
