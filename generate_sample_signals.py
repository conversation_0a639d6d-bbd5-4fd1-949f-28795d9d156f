#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIFXHunter Pro v2.2 - Sample Signal Generator
Generate sample signals with outcomes for testing the performance dashboard
"""

import random
import time
from datetime import datetime, timedelta
from performance_database import PerformanceDatabase

def generate_sample_signals():
    """Generate sample signals with realistic outcomes for testing."""
    print("🎯 Generating Sample Signals for Performance Dashboard Testing")
    print("=" * 60)
    
    # Initialize database
    database = PerformanceDatabase()
    
    # Sample currency pairs
    currency_pairs = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "EURJPY", "GBPJPY"]
    
    # Sample strategies
    strategies = [
        "RSI_Oversold_Bounce",
        "RSI_Overbought_Reversal", 
        "MACD_Bullish_Crossover",
        "MACD_Bearish_Crossover",
        "Multi_Indicator_Confluence",
        "Bollinger_Band_Breakout",
        "Stochastic_Oversold"
    ]
    
    # Sample market conditions
    market_conditions_options = [
        {"trend": "bullish", "volatility": "low", "momentum": "bullish"},
        {"trend": "bearish", "volatility": "medium", "momentum": "bearish"},
        {"trend": "bullish", "volatility": "high", "momentum": "bullish"},
        {"trend": "bearish", "volatility": "low", "momentum": "bearish"},
        {"trend": "neutral", "volatility": "medium", "momentum": "neutral"}
    ]
    
    # Generate signals from the past few hours
    signals_to_generate = 15
    base_time = datetime.now() - timedelta(hours=2)
    
    print(f"📊 Generating {signals_to_generate} sample signals...")
    
    for i in range(signals_to_generate):
        # Random signal parameters
        asset = random.choice(currency_pairs)
        strategy = random.choice(strategies)
        signal_type = random.choice(["CALL", "PUT"])
        confidence = random.randint(60, 95)
        
        # Generate realistic entry price based on asset
        if "JPY" in asset:
            entry_price = round(random.uniform(140, 155), 3)
        else:
            entry_price = round(random.uniform(1.0, 1.3), 5)
        
        # Random market conditions
        market_conditions = random.choice(market_conditions_options)
        
        # Generate signal timestamp (spread over past 2 hours)
        signal_time = base_time + timedelta(minutes=random.randint(0, 120))
        
        # Create signal data
        signal_data = {
            "signal_id": f"SAMPLE_{i+1:03d}_{int(time.time())}",
            "asset": asset,
            "signal": signal_type,
            "expiry": random.choice(["5m", "15m"]),
            "confidence": f"{confidence}%",
            "entry_price": entry_price,
            "strategy_name": strategy,
            "strategy_text": f"Sample strategy: {strategy}",
            "timeframe": "5m",
            "risk_level": random.choice(["Low", "Medium", "High"]),
            "market_conditions": market_conditions,
            "reasoning": [
                f"Sample condition 1 for {strategy}",
                f"Sample condition 2 for {strategy}",
                f"Market trend: {market_conditions['trend']}"
            ],
            "timestamp": signal_time.isoformat()
        }
        
        # Store signal
        success = database.store_signal(signal_data)
        if success:
            print(f"✅ Signal {i+1}: {asset} {signal_type} @ {entry_price} ({confidence}%)")
            
            # Generate outcome for signals older than 30 minutes
            if signal_time < datetime.now() - timedelta(minutes=30):
                # Generate realistic outcome
                win_probability = 0.7 if confidence > 80 else 0.6 if confidence > 70 else 0.5
                is_win = random.random() < win_probability
                
                # Generate exit price
                if "JPY" in asset:
                    price_change = random.uniform(-0.5, 0.5)  # Smaller changes for JPY pairs
                    exit_price = entry_price + price_change
                else:
                    price_change = random.uniform(-0.01, 0.01)  # Typical forex price changes
                    exit_price = entry_price + price_change
                
                # Determine result based on signal type and price movement
                if signal_type == "CALL":
                    if exit_price > entry_price:
                        result = "WIN" if is_win else "LOSS"  # Sometimes even correct direction can lose
                    else:
                        result = "LOSS"
                else:  # PUT
                    if exit_price < entry_price:
                        result = "WIN" if is_win else "LOSS"
                    else:
                        result = "LOSS"
                
                # Calculate profit/loss percentage
                if signal_type == "CALL":
                    profit_loss = ((exit_price - entry_price) / entry_price) * 100
                else:  # PUT
                    profit_loss = ((entry_price - exit_price) / entry_price) * 100
                
                # Adjust profit/loss based on result (binary options are typically fixed return)
                if result == "WIN":
                    profit_loss = abs(profit_loss) if profit_loss != 0 else random.uniform(0.5, 2.0)
                else:
                    profit_loss = -abs(profit_loss) if profit_loss != 0 else -random.uniform(0.5, 2.0)
                
                # Store result
                result_success = database.store_signal_result(
                    signal_id=signal_data["signal_id"],
                    exit_price=exit_price,
                    result=result,
                    profit_loss=profit_loss
                )
                
                if result_success:
                    print(f"   📈 Outcome: {result} @ {exit_price:.5f} ({profit_loss:+.2f}%)")
        else:
            print(f"❌ Failed to store signal {i+1}")
    
    # Update aggregated statistics
    print("\n📊 Updating performance statistics...")
    database.update_strategy_performance()
    
    # Show summary
    print("\n📋 Performance Summary:")
    stats = database.get_performance_stats(days=1)
    overall = stats.get('overall', {})
    
    print(f"Total Signals: {overall.get('total_signals', 0)}")
    print(f"Evaluated Signals: {overall.get('evaluated_signals', 0)}")
    print(f"Overall Win Rate: {overall.get('win_rate', 0):.1f}%")
    print(f"Average Confidence: {overall.get('avg_confidence', 0):.1f}%")
    
    # Strategy breakdown
    strategies_stats = stats.get('by_strategy', [])
    if strategies_stats:
        print(f"\nStrategy Performance:")
        for strategy_stat in strategies_stats[:5]:  # Top 5
            print(f"  {strategy_stat['strategy_name']}: {strategy_stat.get('win_rate', 0):.1f}% ({strategy_stat.get('evaluated', 0)} signals)")
    
    # Asset breakdown
    asset_stats = stats.get('by_asset', [])
    if asset_stats:
        print(f"\nAsset Performance:")
        for asset_stat in asset_stats[:5]:  # Top 5
            print(f"  {asset_stat['asset']}: {asset_stat.get('win_rate', 0):.1f}% ({asset_stat.get('evaluated', 0)} signals)")
    
    print(f"\n🎉 Sample signal generation complete!")
    print(f"📊 Check the AIFXHunter dashboard to see the performance analytics!")
    print(f"🎯 Recent signal results will be displayed in the 'Recent Signal Results' section")
    
    return True

if __name__ == "__main__":
    try:
        success = generate_sample_signals()
        if success:
            print("\n🚀 Sample signals generated successfully!")
            print("💡 Tip: Refresh the AIFXHunter dashboard to see the new performance data")
        else:
            print("\n❌ Sample signal generation failed")
    except Exception as e:
        print(f"\n💥 Error generating sample signals: {e}")
        import traceback
        traceback.print_exc()
