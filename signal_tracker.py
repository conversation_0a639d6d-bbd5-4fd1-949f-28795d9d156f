# -*- coding: utf-8 -*-
# =============================================================================
# AIFXHunter Pro v2.2 - Automated Signal Tracking System
# Real-time signal monitoring and outcome evaluation
# =============================================================================

import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable

from performance_database import PerformanceDatabase
from enhanced_price_manager import price_manager

class SignalTracker:
    """
    Automated signal tracking system that monitors signal outcomes
    and updates performance metrics in real-time.
    """
    
    def __init__(self, database: PerformanceDatabase):
        self.database = database
        self.tracking_active = False
        self.tracking_thread = None
        self.check_interval = 30  # Check every 30 seconds
        self.price_cache = {}  # Cache for recent price data
        self.callbacks = []  # Callbacks for performance updates
        
    def start_tracking(self):
        """Start the automated signal tracking system."""
        if self.tracking_active:
            print("Signal tracking is already active")
            return
        
        self.tracking_active = True
        self.tracking_thread = threading.Thread(target=self._tracking_loop, daemon=True)
        self.tracking_thread.start()
        print("✅ Signal tracking system started")
    
    def stop_tracking(self):
        """Stop the automated signal tracking system."""
        self.tracking_active = False
        if self.tracking_thread:
            self.tracking_thread.join(timeout=5)
        print("🛑 Signal tracking system stopped")
    
    def add_performance_callback(self, callback: Callable):
        """Add a callback function to be called when performance is updated."""
        self.callbacks.append(callback)
    
    def _tracking_loop(self):
        """Main tracking loop that runs in a separate thread."""
        print("🔄 Signal tracking loop started")
        
        while self.tracking_active:
            try:
                # Check for expired signals
                pending_signals = self.database.get_pending_signals()
                
                if pending_signals:
                    print(f"📊 Evaluating {len(pending_signals)} expired signals...")
                    
                    for signal in pending_signals:
                        self._evaluate_signal(signal)
                    
                    # Update aggregated statistics
                    self.database.update_strategy_performance()
                    
                    # Notify callbacks about performance updates
                    self._notify_callbacks()
                
                # Sleep for the check interval
                time.sleep(self.check_interval)
                
            except Exception as e:
                print(f"Error in tracking loop: {e}")
                time.sleep(self.check_interval)
    
    def _evaluate_signal(self, signal: Dict[str, Any]):
        """Evaluate a single expired signal with enhanced error handling."""
        try:
            asset = signal['asset']
            signal_id = signal['signal_id']
            signal_type = signal['signal_type']  # CALL or PUT
            entry_price = signal['entry_price']
            expiry_time = signal['expiry_time']

            # Check if this signal has already failed evaluation
            if price_manager.is_signal_failed(signal_id):
                print(f"⏭️ Skipping previously failed signal {signal_id}")
                return

            # Get the exit price at expiry time with retry limits
            exit_price = self._get_price_at_expiry(asset, expiry_time, signal_id)

            if exit_price is None:
                print(f"⚠️ Could not get exit price for {signal_id} - marking as failed")
                price_manager.mark_signal_as_failed(signal_id)
                return

            # Determine the result based on binary options rules
            result = self._determine_result(signal_type, entry_price, exit_price)

            # Calculate profit/loss percentage
            profit_loss = self._calculate_profit_loss(signal_type, entry_price, exit_price)

            # Store the result
            success = self.database.store_signal_result(
                signal_id=signal_id,
                exit_price=exit_price,
                result=result,
                profit_loss=profit_loss,
                evaluation_method="AUTO"
            )

            if success:
                print(f"✅ Signal {signal_id} evaluated: {result} ({profit_loss:+.2f}%)")
            else:
                print(f"❌ Failed to store result for signal {signal_id}")

        except Exception as e:
            print(f"Error evaluating signal {signal.get('signal_id', 'unknown')}: {e}")
            # Mark signal as failed to prevent repeated attempts
            if 'signal_id' in signal:
                price_manager.mark_signal_as_failed(signal['signal_id'])
    
    def _get_price_at_expiry(self, asset: str, expiry_time: datetime, signal_id: str) -> Optional[float]:
        """Get the market price at the signal expiry time with enhanced error handling."""
        try:
            # Convert expiry_time to datetime if it's a string
            if isinstance(expiry_time, str):
                expiry_time = datetime.fromisoformat(expiry_time)

            # Check if we need to get current price (for recently expired signals)
            time_diff = (datetime.now() - expiry_time).total_seconds()

            if abs(time_diff) <= 300:  # Within 5 minutes, get current price
                return price_manager.get_price_with_retry_limit(asset, signal_id, max_retries=2)
            else:
                # For older signals, we might not have exact historical data
                # In a production system, you'd integrate with a historical data provider
                print(f"⚠️ Signal expired {time_diff/60:.1f} minutes ago, using current price as approximation")
                return price_manager.get_price_with_retry_limit(asset, signal_id, max_retries=1)

        except Exception as e:
            print(f"Error getting price at expiry for {asset}: {e}")
            return None
    
    def _get_current_price(self, asset: str) -> Optional[float]:
        """Get the current market price for an asset using enhanced price manager."""
        try:
            # Delegate to the enhanced price manager
            return price_manager.get_current_price(asset)

        except Exception as e:
            print(f"Error getting current price for {asset}: {e}")
            return None
    
    def _determine_result(self, signal_type: str, entry_price: float, exit_price: float) -> str:
        """Determine if the signal was a win, loss, or tie based on binary options rules."""
        try:
            price_diff = exit_price - entry_price
            
            if signal_type.upper() == "CALL":
                # CALL wins if price goes up
                if price_diff > 0:
                    return "WIN"
                elif price_diff < 0:
                    return "LOSS"
                else:
                    return "TIE"
            
            elif signal_type.upper() == "PUT":
                # PUT wins if price goes down
                if price_diff < 0:
                    return "WIN"
                elif price_diff > 0:
                    return "LOSS"
                else:
                    return "TIE"
            
            else:
                print(f"Unknown signal type: {signal_type}")
                return "UNKNOWN"
                
        except Exception as e:
            print(f"Error determining result: {e}")
            return "ERROR"
    
    def _calculate_profit_loss(self, signal_type: str, entry_price: float, exit_price: float) -> float:
        """Calculate the profit/loss percentage."""
        try:
            price_change_pct = ((exit_price - entry_price) / entry_price) * 100
            
            if signal_type.upper() == "CALL":
                return price_change_pct  # Positive if price went up
            elif signal_type.upper() == "PUT":
                return -price_change_pct  # Positive if price went down
            else:
                return 0.0
                
        except Exception as e:
            print(f"Error calculating profit/loss: {e}")
            return 0.0
    
    def _notify_callbacks(self):
        """Notify all registered callbacks about performance updates."""
        try:
            for callback in self.callbacks:
                try:
                    callback()
                except Exception as e:
                    print(f"Error in performance callback: {e}")
        except Exception as e:
            print(f"Error notifying callbacks: {e}")
    
    def track_signal(self, signal_data: Dict[str, Any]) -> bool:
        """Manually track a specific signal (alternative to automatic tracking)."""
        try:
            # Store the signal in the database
            success = self.database.store_signal(signal_data)
            
            if success:
                print(f"📊 Signal {signal_data.get('signal_id', 'unknown')} added to tracking")
                return True
            else:
                print(f"❌ Failed to track signal {signal_data.get('signal_id', 'unknown')}")
                return False
                
        except Exception as e:
            print(f"Error tracking signal: {e}")
            return False
    
    def get_tracking_status(self) -> Dict[str, Any]:
        """Get the current status of the tracking system with enhanced price manager info."""
        try:
            pending_signals = self.database.get_pending_signals()
            rolling_24h = self.database.get_rolling_performance(hours=24)
            rolling_7d = self.database.get_rolling_performance(hours=168)  # 7 days

            # Get price manager status
            price_manager_status = price_manager.get_status()

            return {
                'tracking_active': self.tracking_active,
                'pending_evaluations': len(pending_signals),
                'check_interval_seconds': self.check_interval,
                'price_cache_size': len(self.price_cache),
                'callbacks_registered': len(self.callbacks),
                'performance_24h': rolling_24h,
                'performance_7d': rolling_7d,
                'price_manager': price_manager_status
            }

        except Exception as e:
            print(f"Error getting tracking status: {e}")
            return {}
    
    def force_evaluation_check(self):
        """Force an immediate check for signals that need evaluation."""
        try:
            pending_signals = self.database.get_pending_signals()
            
            if not pending_signals:
                print("📊 No signals pending evaluation")
                return
            
            print(f"🔄 Force evaluating {len(pending_signals)} signals...")
            
            for signal in pending_signals:
                self._evaluate_signal(signal)
            
            # Update aggregated statistics
            self.database.update_strategy_performance()
            
            # Notify callbacks
            self._notify_callbacks()
            
            print("✅ Force evaluation completed")
            
        except Exception as e:
            print(f"Error in force evaluation: {e}")
    
    def get_recent_evaluations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get the most recent signal evaluations."""
        try:
            import sqlite3
            with sqlite3.connect(self.database.db_path) as conn:
                    cursor = conn.cursor()

                    cursor.execute("""
                    SELECT
                        s.signal_id, s.asset, s.signal_type, s.entry_price,
                        s.confidence, s.strategy_name, sr.exit_price,
                        sr.result, sr.profit_loss, sr.evaluated_at, s.timestamp
                    FROM signals s
                    JOIN signal_results sr ON s.signal_id = sr.signal_id
                    ORDER BY sr.evaluated_at DESC
                    LIMIT ?
                    """, (limit,))

                    columns = [desc[0] for desc in cursor.description]
                    results = []

                    for row in cursor.fetchall():
                        results.append(dict(zip(columns, row)))

                    return results

        except Exception as e:
            print(f"Error getting recent evaluations: {e}")
            return []

    def get_recent_evaluations_by_strategy(self, strategy_name: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get the most recent signal evaluations for a specific strategy."""
        try:
            import sqlite3
            with sqlite3.connect(self.database.db_path) as conn:
                    cursor = conn.cursor()

                    cursor.execute("""
                    SELECT
                        s.signal_id, s.asset, s.signal_type, s.entry_price,
                        s.confidence, s.strategy_name, sr.exit_price,
                        sr.result, sr.profit_loss, sr.evaluated_at, s.timestamp
                    FROM signals s
                    JOIN signal_results sr ON s.signal_id = sr.signal_id
                    WHERE s.strategy_name = ?
                    ORDER BY sr.evaluated_at DESC
                    LIMIT ?
                    """, (strategy_name, limit))

                    columns = [desc[0] for desc in cursor.description]
                    results = []

                    for row in cursor.fetchall():
                        results.append(dict(zip(columns, row)))

                    return results

        except Exception as e:
            print(f"Error getting recent evaluations by strategy: {e}")
            return []
    
    def cleanup_old_cache(self):
        """Clean up old entries from both local and price manager caches."""
        try:
            now = datetime.now()
            expired_keys = []

            # Clean local cache
            for key, data in self.price_cache.items():
                if (now - data['timestamp']).seconds > 300:  # Remove entries older than 5 minutes
                    expired_keys.append(key)

            for key in expired_keys:
                del self.price_cache[key]

            if expired_keys:
                print(f"🧹 Cleaned up {len(expired_keys)} expired local cache entries")

            # Clean price manager cache
            price_manager.cleanup_cache()

        except Exception as e:
            print(f"Error cleaning up cache: {e}")
    
    def set_check_interval(self, seconds: int):
        """Set the interval for checking expired signals."""
        if seconds < 10:
            print("⚠️ Minimum check interval is 10 seconds")
            seconds = 10
        elif seconds > 300:
            print("⚠️ Maximum check interval is 300 seconds")
            seconds = 300
        
        self.check_interval = seconds
        print(f"✅ Check interval set to {seconds} seconds")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a comprehensive performance summary."""
        try:
            # Get various time period performances
            performance_24h = self.database.get_rolling_performance(hours=24)
            performance_7d = self.database.get_rolling_performance(hours=168)
            performance_30d = self.database.get_performance_stats(days=30)
            
            # Get tracking status
            status = self.get_tracking_status()
            
            return {
                'tracking_status': status,
                'performance': {
                    '24_hours': performance_24h,
                    '7_days': performance_7d,
                    '30_days': performance_30d
                },
                'summary_generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Error getting performance summary: {e}")
            return {}
