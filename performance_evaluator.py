# -*- coding: utf-8 -*-
# =============================================================================
# AIFXHunter Pro v2.2 - Performance Evaluator & Adaptive Learning System
# Advanced analytics and strategy optimization based on historical performance
# =============================================================================

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json
from collections import defaultdict

from performance_database import PerformanceDatabase

class PerformanceEvaluator:
    """
    Advanced performance evaluation system with adaptive learning capabilities.
    Analyzes historical data to optimize strategies and improve signal quality.
    """
    
    def __init__(self, database: PerformanceDatabase):
        self.database = database
        self.min_samples_for_analysis = 10  # Minimum signals needed for reliable analysis
        self.confidence_adjustment_factor = 0.1  # How much to adjust confidence based on performance
        self.performance_thresholds = {
            'excellent': 80.0,
            'good': 70.0,
            'acceptable': 60.0,
            'poor': 50.0
        }
    
    def evaluate_strategy_performance(self, strategy_name: str, days: int = 30) -> Dict[str, Any]:
        """Comprehensive evaluation of a specific strategy's performance."""
        try:
            stats = self.database.get_performance_stats(days=days)
            strategy_stats = next((s for s in stats.get('by_strategy', []) if s['strategy_name'] == strategy_name), None)
            
            if not strategy_stats:
                return {
                    'strategy_name': strategy_name,
                    'status': 'no_data',
                    'message': 'No performance data available for this strategy'
                }
            
            # Basic metrics
            win_rate = strategy_stats.get('win_rate', 0)
            total_signals = strategy_stats.get('total_signals', 0)
            evaluated_signals = strategy_stats.get('evaluated', 0)
            avg_confidence = strategy_stats.get('avg_confidence', 0)
            
            # Performance classification
            performance_level = self._classify_performance(win_rate)
            
            # Reliability score (based on sample size and consistency)
            reliability_score = self._calculate_reliability_score(total_signals, evaluated_signals, win_rate)
            
            # Get detailed breakdown
            breakdown = self._get_strategy_breakdown(strategy_name, days)
            
            # Generate recommendations
            recommendations = self._generate_strategy_recommendations(strategy_stats, breakdown)
            
            return {
                'strategy_name': strategy_name,
                'evaluation_period_days': days,
                'metrics': {
                    'win_rate': win_rate,
                    'total_signals': total_signals,
                    'evaluated_signals': evaluated_signals,
                    'avg_confidence': avg_confidence,
                    'reliability_score': reliability_score
                },
                'performance_level': performance_level,
                'breakdown': breakdown,
                'recommendations': recommendations,
                'status': 'analyzed',
                'evaluated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Error evaluating strategy {strategy_name}: {e}")
            return {'strategy_name': strategy_name, 'status': 'error', 'error': str(e)}
    
    def _classify_performance(self, win_rate: float) -> str:
        """Classify performance level based on win rate."""
        if win_rate >= self.performance_thresholds['excellent']:
            return 'excellent'
        elif win_rate >= self.performance_thresholds['good']:
            return 'good'
        elif win_rate >= self.performance_thresholds['acceptable']:
            return 'acceptable'
        elif win_rate >= self.performance_thresholds['poor']:
            return 'poor'
        else:
            return 'very_poor'
    
    def _calculate_reliability_score(self, total_signals: int, evaluated_signals: int, win_rate: float) -> float:
        """Calculate a reliability score based on sample size and performance consistency."""
        try:
            # Sample size factor (more signals = more reliable)
            sample_factor = min(1.0, evaluated_signals / 50)  # Max reliability at 50+ signals
            
            # Evaluation rate factor (higher evaluation rate = more reliable)
            eval_rate = evaluated_signals / total_signals if total_signals > 0 else 0
            eval_factor = min(1.0, eval_rate)
            
            # Performance factor (extreme win rates might be less reliable with small samples)
            if evaluated_signals < self.min_samples_for_analysis:
                perf_factor = 0.5  # Low reliability for small samples
            else:
                # Moderate win rates (40-80%) are considered more reliable
                if 40 <= win_rate <= 80:
                    perf_factor = 1.0
                else:
                    perf_factor = 0.8
            
            reliability_score = (sample_factor * 0.4 + eval_factor * 0.3 + perf_factor * 0.3) * 100
            
            return round(reliability_score, 2)
            
        except Exception as e:
            print(f"Error calculating reliability score: {e}")
            return 0.0
    
    def _get_strategy_breakdown(self, strategy_name: str, days: int) -> Dict[str, Any]:
        """Get detailed breakdown of strategy performance by various factors."""
        try:
            import sqlite3
            
            with sqlite3.connect(self.database.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # Performance by asset
                cursor.execute("""
                SELECT 
                    s.asset,
                    COUNT(s.id) as total,
                    SUM(CASE WHEN sr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    COUNT(sr.id) as evaluated
                FROM signals s
                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE s.strategy_name = ? AND s.timestamp >= ?
                GROUP BY s.asset
                ORDER BY wins DESC
                """, (strategy_name, cutoff_date))
                
                asset_performance = []
                for row in cursor.fetchall():
                    asset, total, wins, evaluated = row
                    win_rate = (wins / evaluated * 100) if evaluated > 0 else 0
                    asset_performance.append({
                        'asset': asset,
                        'total_signals': total,
                        'wins': wins or 0,
                        'evaluated': evaluated,
                        'win_rate': win_rate
                    })
                
                # Performance by confidence level
                cursor.execute("""
                SELECT 
                    CASE 
                        WHEN s.confidence >= 80 THEN 'high'
                        WHEN s.confidence >= 60 THEN 'medium'
                        ELSE 'low'
                    END as confidence_level,
                    COUNT(s.id) as total,
                    SUM(CASE WHEN sr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    COUNT(sr.id) as evaluated,
                    AVG(s.confidence) as avg_confidence
                FROM signals s
                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE s.strategy_name = ? AND s.timestamp >= ?
                GROUP BY confidence_level
                """, (strategy_name, cutoff_date))
                
                confidence_performance = []
                for row in cursor.fetchall():
                    level, total, wins, evaluated, avg_conf = row
                    win_rate = (wins / evaluated * 100) if evaluated > 0 else 0
                    confidence_performance.append({
                        'confidence_level': level,
                        'total_signals': total,
                        'wins': wins or 0,
                        'evaluated': evaluated,
                        'win_rate': win_rate,
                        'avg_confidence': avg_conf or 0
                    })
                
                # Performance by timeframe
                cursor.execute("""
                SELECT 
                    s.timeframe,
                    COUNT(s.id) as total,
                    SUM(CASE WHEN sr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    COUNT(sr.id) as evaluated
                FROM signals s
                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE s.strategy_name = ? AND s.timestamp >= ?
                GROUP BY s.timeframe
                """, (strategy_name, cutoff_date))
                
                timeframe_performance = []
                for row in cursor.fetchall():
                    timeframe, total, wins, evaluated = row
                    win_rate = (wins / evaluated * 100) if evaluated > 0 else 0
                    timeframe_performance.append({
                        'timeframe': timeframe,
                        'total_signals': total,
                        'wins': wins or 0,
                        'evaluated': evaluated,
                        'win_rate': win_rate
                    })
                
                return {
                    'by_asset': asset_performance,
                    'by_confidence': confidence_performance,
                    'by_timeframe': timeframe_performance
                }
                
        except Exception as e:
            print(f"Error getting strategy breakdown: {e}")
            return {}
    
    def _generate_strategy_recommendations(self, strategy_stats: Dict[str, Any], breakdown: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on performance analysis."""
        recommendations = []
        
        try:
            win_rate = strategy_stats.get('win_rate', 0)
            total_signals = strategy_stats.get('total_signals', 0)
            evaluated = strategy_stats.get('evaluated', 0)
            
            # Sample size recommendations
            if evaluated < self.min_samples_for_analysis:
                recommendations.append(f"⚠️ Need more data: Only {evaluated} evaluated signals. Collect at least {self.min_samples_for_analysis} for reliable analysis.")
            
            # Performance recommendations
            if win_rate < self.performance_thresholds['poor']:
                recommendations.append("🔴 Poor performance: Consider disabling this strategy or reviewing conditions.")
            elif win_rate < self.performance_thresholds['acceptable']:
                recommendations.append("🟡 Below average performance: Review strategy conditions and market applicability.")
            elif win_rate >= self.performance_thresholds['excellent']:
                recommendations.append("🟢 Excellent performance: Consider increasing position size or frequency.")
            
            # Asset-specific recommendations
            asset_perf = breakdown.get('by_asset', [])
            if asset_perf:
                best_assets = [a for a in asset_perf if a['win_rate'] > 70 and a['evaluated'] >= 3]
                worst_assets = [a for a in asset_perf if a['win_rate'] < 50 and a['evaluated'] >= 3]
                
                if best_assets:
                    best_asset_names = [a['asset'] for a in best_assets[:3]]
                    recommendations.append(f"📈 Best performing assets: {', '.join(best_asset_names)}. Focus on these pairs.")
                
                if worst_assets:
                    worst_asset_names = [a['asset'] for a in worst_assets[:3]]
                    recommendations.append(f"📉 Poor performing assets: {', '.join(worst_asset_names)}. Consider excluding these pairs.")
            
            # Confidence level recommendations
            conf_perf = breakdown.get('by_confidence', [])
            if conf_perf:
                high_conf = next((c for c in conf_perf if c['confidence_level'] == 'high'), None)
                low_conf = next((c for c in conf_perf if c['confidence_level'] == 'low'), None)
                
                if high_conf and high_conf['win_rate'] > 75:
                    recommendations.append("🎯 High confidence signals perform well. Consider raising minimum confidence threshold.")
                
                if low_conf and low_conf['win_rate'] < 50:
                    recommendations.append("⚠️ Low confidence signals underperform. Consider filtering out signals below 60% confidence.")
            
            # Timeframe recommendations
            tf_perf = breakdown.get('by_timeframe', [])
            if tf_perf:
                best_tf = max(tf_perf, key=lambda x: x['win_rate']) if tf_perf else None
                if best_tf and best_tf['win_rate'] > win_rate + 10:
                    recommendations.append(f"⏰ {best_tf['timeframe']} timeframe shows best results ({best_tf['win_rate']:.1f}% win rate).")
            
            if not recommendations:
                recommendations.append("✅ Strategy performance is within normal parameters. Continue monitoring.")
            
        except Exception as e:
            print(f"Error generating recommendations: {e}")
            recommendations.append("❌ Error generating recommendations. Manual review recommended.")
        
        return recommendations
    
    def get_market_condition_analysis(self, days: int = 30) -> Dict[str, Any]:
        """Analyze performance under different market conditions."""
        try:
            import sqlite3
            
            with sqlite3.connect(self.database.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # Get signals with market conditions
                cursor.execute("""
                SELECT 
                    s.market_conditions,
                    COUNT(s.id) as total,
                    SUM(CASE WHEN sr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    COUNT(sr.id) as evaluated
                FROM signals s
                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE s.timestamp >= ? AND s.market_conditions IS NOT NULL
                GROUP BY s.market_conditions
                """, (cutoff_date,))
                
                condition_analysis = {}
                
                for row in cursor.fetchall():
                    conditions_json, total, wins, evaluated = row
                    
                    try:
                        conditions = json.loads(conditions_json)
                        win_rate = (wins / evaluated * 100) if evaluated > 0 else 0
                        
                        # Extract individual conditions
                        trend = conditions.get('trend', 'unknown')
                        volatility = conditions.get('volatility', 'unknown')
                        momentum = conditions.get('momentum', 'unknown')
                        
                        key = f"{trend}_{volatility}_{momentum}"
                        condition_analysis[key] = {
                            'trend': trend,
                            'volatility': volatility,
                            'momentum': momentum,
                            'total_signals': total,
                            'wins': wins or 0,
                            'evaluated': evaluated,
                            'win_rate': win_rate
                        }
                        
                    except json.JSONDecodeError:
                        continue
                
                # Find best and worst conditions
                if condition_analysis:
                    best_condition = max(condition_analysis.values(), key=lambda x: x['win_rate'])
                    worst_condition = min(condition_analysis.values(), key=lambda x: x['win_rate'])
                else:
                    best_condition = worst_condition = None
                
                return {
                    'analysis_period_days': days,
                    'conditions': condition_analysis,
                    'best_condition': best_condition,
                    'worst_condition': worst_condition,
                    'total_conditions_analyzed': len(condition_analysis)
                }
                
        except Exception as e:
            print(f"Error analyzing market conditions: {e}")
            return {}
    
    def calculate_adaptive_confidence_adjustment(self, strategy_name: str, base_confidence: float) -> float:
        """Calculate confidence adjustment based on historical performance."""
        try:
            # Get recent performance for this strategy
            evaluation = self.evaluate_strategy_performance(strategy_name, days=7)  # Last week
            
            if evaluation.get('status') != 'analyzed':
                return base_confidence  # No adjustment if no data
            
            metrics = evaluation.get('metrics', {})
            win_rate = metrics.get('win_rate', 0)
            reliability_score = metrics.get('reliability_score', 0)
            evaluated_signals = metrics.get('evaluated_signals', 0)
            
            # Only adjust if we have enough data
            if evaluated_signals < 5:
                return base_confidence
            
            # Calculate adjustment factor
            if win_rate > 75 and reliability_score > 70:
                # High performing strategy - boost confidence
                adjustment = min(10, (win_rate - 75) * 0.5)
            elif win_rate < 55:
                # Poor performing strategy - reduce confidence
                adjustment = max(-15, (win_rate - 55) * 0.5)
            else:
                # Average performance - small adjustment
                adjustment = (win_rate - 65) * 0.2
            
            # Apply reliability factor
            adjustment *= (reliability_score / 100)
            
            # Apply the adjustment
            adjusted_confidence = base_confidence + adjustment
            
            # Ensure confidence stays within reasonable bounds
            adjusted_confidence = max(10, min(95, adjusted_confidence))
            
            return adjusted_confidence
            
        except Exception as e:
            print(f"Error calculating confidence adjustment: {e}")
            return base_confidence
    
    def get_strategy_recommendations(self, market_conditions: Dict[str, str]) -> List[Dict[str, Any]]:
        """Recommend best strategies based on current market conditions and historical performance."""
        try:
            # Get all strategy performance data
            stats = self.database.get_performance_stats(days=30)
            strategies = stats.get('by_strategy', [])
            
            # Get market condition analysis
            market_analysis = self.get_market_condition_analysis(days=30)
            conditions = market_analysis.get('conditions', {})
            
            recommendations = []
            
            for strategy in strategies:
                strategy_name = strategy['strategy_name']
                win_rate = strategy.get('win_rate', 0)
                evaluated = strategy.get('evaluated', 0)
                
                # Skip strategies with insufficient data
                if evaluated < 5:
                    continue
                
                # Find matching market conditions
                current_condition_key = f"{market_conditions.get('trend', 'unknown')}_{market_conditions.get('volatility', 'unknown')}_{market_conditions.get('momentum', 'unknown')}"
                condition_performance = conditions.get(current_condition_key, {})
                
                # Calculate recommendation score
                base_score = win_rate
                condition_bonus = condition_performance.get('win_rate', win_rate) - win_rate
                sample_size_factor = min(1.0, evaluated / 20)  # Bonus for more data
                
                recommendation_score = (base_score + condition_bonus * 0.3) * sample_size_factor
                
                recommendations.append({
                    'strategy_name': strategy_name,
                    'win_rate': win_rate,
                    'recommendation_score': recommendation_score,
                    'evaluated_signals': evaluated,
                    'condition_match': condition_performance.get('win_rate', 0),
                    'performance_level': self._classify_performance(win_rate)
                })
            
            # Sort by recommendation score
            recommendations.sort(key=lambda x: x['recommendation_score'], reverse=True)
            
            return recommendations[:5]  # Top 5 recommendations
            
        except Exception as e:
            print(f"Error getting strategy recommendations: {e}")
            return []
    
    def generate_performance_report(self, days: int = 30) -> Dict[str, Any]:
        """Generate a comprehensive performance report."""
        try:
            # Get overall statistics
            overall_stats = self.database.get_performance_stats(days=days)
            
            # Get rolling performance
            rolling_24h = self.database.get_rolling_performance(hours=24)
            rolling_7d = self.database.get_rolling_performance(hours=168)
            
            # Get market condition analysis
            market_analysis = self.get_market_condition_analysis(days=days)
            
            # Analyze each strategy
            strategy_analyses = []
            for strategy in overall_stats.get('by_strategy', []):
                analysis = self.evaluate_strategy_performance(strategy['strategy_name'], days=days)
                strategy_analyses.append(analysis)
            
            # Generate insights
            insights = self._generate_insights(overall_stats, strategy_analyses, market_analysis)
            
            return {
                'report_generated_at': datetime.now().isoformat(),
                'analysis_period_days': days,
                'overall_performance': overall_stats,
                'rolling_performance': {
                    '24_hours': rolling_24h,
                    '7_days': rolling_7d
                },
                'strategy_analyses': strategy_analyses,
                'market_condition_analysis': market_analysis,
                'insights_and_recommendations': insights
            }
            
        except Exception as e:
            print(f"Error generating performance report: {e}")
            return {}
    
    def _generate_insights(self, overall_stats: Dict[str, Any], strategy_analyses: List[Dict[str, Any]], 
                          market_analysis: Dict[str, Any]) -> List[str]:
        """Generate high-level insights from the performance data."""
        insights = []
        
        try:
            overall = overall_stats.get('overall', {})
            overall_win_rate = overall.get('win_rate', 0)
            
            # Overall performance insight
            if overall_win_rate > 70:
                insights.append(f"🎉 Excellent overall performance with {overall_win_rate:.1f}% win rate")
            elif overall_win_rate > 60:
                insights.append(f"✅ Good overall performance with {overall_win_rate:.1f}% win rate")
            else:
                insights.append(f"⚠️ Performance needs improvement: {overall_win_rate:.1f}% win rate")
            
            # Strategy insights
            if strategy_analyses:
                excellent_strategies = [s for s in strategy_analyses if s.get('performance_level') == 'excellent']
                poor_strategies = [s for s in strategy_analyses if s.get('performance_level') in ['poor', 'very_poor']]
                
                if excellent_strategies:
                    strategy_names = [s['strategy_name'] for s in excellent_strategies[:3]]
                    insights.append(f"🌟 Top performing strategies: {', '.join(strategy_names)}")
                
                if poor_strategies:
                    strategy_names = [s['strategy_name'] for s in poor_strategies[:2]]
                    insights.append(f"🔴 Underperforming strategies need review: {', '.join(strategy_names)}")
            
            # Market condition insights
            best_condition = market_analysis.get('best_condition')
            worst_condition = market_analysis.get('worst_condition')
            
            if best_condition and best_condition['win_rate'] > 75:
                insights.append(f"📈 Best market conditions: {best_condition['trend']} trend, {best_condition['volatility']} volatility ({best_condition['win_rate']:.1f}% win rate)")
            
            if worst_condition and worst_condition['win_rate'] < 50:
                insights.append(f"📉 Avoid trading in: {worst_condition['trend']} trend, {worst_condition['volatility']} volatility ({worst_condition['win_rate']:.1f}% win rate)")
            
        except Exception as e:
            print(f"Error generating insights: {e}")
            insights.append("❌ Error generating insights")
        
        return insights
