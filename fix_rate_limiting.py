#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIFXHunter Pro v2.2 - Rate Limiting Fix Script
Comprehensive solution to fix TradingView API rate limiting issues
"""

import time
import sqlite3
from datetime import datetime, timedelta
from enhanced_price_manager import price_manager
from performance_database import PerformanceDatabase
from api_diagnostics import api_diagnostics

def fix_rate_limiting_issues():
    """Comprehensive fix for rate limiting and signal evaluation issues."""
    print("🔧 AIFXHunter Pro v2.2 - Rate Limiting Fix Script")
    print("=" * 60)
    
    # Step 1: Run initial diagnostics
    print("\n📊 Step 1: Running initial diagnostics...")
    initial_diagnostics = api_diagnostics.run_comprehensive_diagnostic()
    
    print(f"   Circuit Breaker: {'🚨 ACTIVE' if initial_diagnostics['circuit_breaker_status']['active'] else '✅ Inactive'}")
    print(f"   Pending Signals: {initial_diagnostics['signal_evaluation_status']['pending_signals']['total_pending']}")
    print(f"   Failed Signals: {initial_diagnostics['signal_evaluation_status']['failed_signals_count']}")
    print(f"   Rate Usage: {initial_diagnostics['rate_limiting_analysis']['usage_percentage']}%")
    
    # Step 2: Reset circuit breaker if active
    if initial_diagnostics['circuit_breaker_status']['active']:
        print("\n🔄 Step 2: Resetting circuit breaker...")
        price_manager._reset_circuit_breaker()
        print("   ✅ Circuit breaker reset")
    else:
        print("\n✅ Step 2: Circuit breaker already inactive")
    
    # Step 3: Clear failed signals
    print("\n🧹 Step 3: Clearing failed signals...")
    failed_count = len(price_manager.failed_signals)
    price_manager.failed_signals.clear()
    print(f"   ✅ Cleared {failed_count} failed signal records")
    
    # Step 4: Clean up caches
    print("\n🧹 Step 4: Cleaning up caches...")
    price_manager.cleanup_cache()
    print("   ✅ Price manager cache cleaned")
    
    # Step 5: Mark old pending signals as failed to prevent retry loops
    print("\n🚫 Step 5: Marking old pending signals as failed...")
    db = PerformanceDatabase()
    pending_signals = db.get_pending_signals()
    
    old_signals_marked = 0
    cutoff_time = datetime.now() - timedelta(hours=1)  # Mark signals older than 1 hour as failed
    
    for signal in pending_signals:
        try:
            expiry_time = signal.get('expiry_time')
            if expiry_time:
                if isinstance(expiry_time, str):
                    expiry_dt = datetime.fromisoformat(expiry_time)
                else:
                    expiry_dt = expiry_time
                
                if expiry_dt < cutoff_time:
                    # Mark as failed in database
                    signal_id = signal.get('signal_id')
                    if signal_id:
                        success = db.store_signal_result(
                            signal_id=signal_id,
                            exit_price=signal.get('entry_price', 1.0),  # Use entry price as fallback
                            result="TIMEOUT",
                            profit_loss=0.0,
                            evaluation_method="TIMEOUT_CLEANUP"
                        )
                        if success:
                            old_signals_marked += 1
        except Exception as e:
            print(f"   ⚠️ Error processing signal: {e}")
    
    print(f"   ✅ Marked {old_signals_marked} old signals as timed out")
    
    # Step 6: Optimize rate limiting settings
    print("\n⚙️ Step 6: Optimizing rate limiting settings...")
    
    # Reduce API call frequency
    price_manager.max_calls_per_minute = 10  # More conservative limit
    price_manager.min_delay_between_calls = 6  # Longer delay between calls
    price_manager.cache_duration = timedelta(minutes=5)  # Longer cache duration
    
    print("   ✅ Rate limiting settings optimized:")
    print(f"      - Max calls per minute: {price_manager.max_calls_per_minute}")
    print(f"      - Min delay between calls: {price_manager.min_delay_between_calls}s")
    print(f"      - Cache duration: {price_manager.cache_duration.total_seconds()/60:.1f} minutes")
    
    # Step 7: Test API connectivity
    print("\n🔍 Step 7: Testing API connectivity...")
    test_pairs = ['EURUSD', 'GBPUSD', 'USDJPY']
    successful_tests = 0
    
    for pair in test_pairs:
        print(f"   Testing {pair}...", end=" ")
        try:
            price = price_manager.get_current_price(pair)
            if price:
                print(f"✅ {price:.5f}")
                successful_tests += 1
            else:
                print("❌ Failed")
            time.sleep(2)  # Wait between tests
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"   📊 API Test Results: {successful_tests}/{len(test_pairs)} successful")
    
    # Step 8: Run final diagnostics
    print("\n📊 Step 8: Running final diagnostics...")
    time.sleep(5)  # Wait a bit for things to settle
    final_diagnostics = api_diagnostics.run_comprehensive_diagnostic()
    
    print(f"   Circuit Breaker: {'🚨 ACTIVE' if final_diagnostics['circuit_breaker_status']['active'] else '✅ Inactive'}")
    print(f"   Pending Signals: {final_diagnostics['signal_evaluation_status']['pending_signals']['total_pending']}")
    print(f"   Failed Signals: {final_diagnostics['signal_evaluation_status']['failed_signals_count']}")
    print(f"   Rate Usage: {final_diagnostics['rate_limiting_analysis']['usage_percentage']}%")
    
    # Step 9: Generate recommendations
    print("\n💡 Step 9: Recommendations for preventing future issues:")
    recommendations = final_diagnostics['recommendations']
    for i, rec in enumerate(recommendations[:8], 1):  # Show top 8 recommendations
        print(f"   {i}. {rec}")
    
    # Step 10: Summary
    print("\n" + "=" * 60)
    print("🎉 RATE LIMITING FIX COMPLETED")
    print("=" * 60)
    
    improvement_metrics = {
        'circuit_breaker_fixed': initial_diagnostics['circuit_breaker_status']['active'] and not final_diagnostics['circuit_breaker_status']['active'],
        'failed_signals_cleared': failed_count,
        'old_signals_cleaned': old_signals_marked,
        'api_tests_passed': successful_tests,
        'rate_usage_improved': initial_diagnostics['rate_limiting_analysis']['usage_percentage'] > final_diagnostics['rate_limiting_analysis']['usage_percentage']
    }
    
    print(f"✅ Circuit Breaker Fixed: {'Yes' if improvement_metrics['circuit_breaker_fixed'] else 'N/A'}")
    print(f"✅ Failed Signals Cleared: {improvement_metrics['failed_signals_cleared']}")
    print(f"✅ Old Signals Cleaned: {improvement_metrics['old_signals_cleaned']}")
    print(f"✅ API Tests Passed: {improvement_metrics['api_tests_passed']}/{len(test_pairs)}")
    print(f"✅ Rate Usage Improved: {'Yes' if improvement_metrics['rate_usage_improved'] else 'Stable'}")
    
    print("\n🚀 System should now operate more reliably with reduced API rate limiting!")
    print("💡 Monitor the system for 10-15 minutes to ensure stable operation.")
    
    return improvement_metrics

def monitor_system_health(duration_minutes: int = 10):
    """Monitor system health after applying fixes."""
    print(f"\n📊 Monitoring system health for {duration_minutes} minutes...")
    print("Press Ctrl+C to stop monitoring early")
    
    try:
        monitoring_results = api_diagnostics.monitor_api_health(duration_minutes)
        
        print("\n📈 Monitoring Results:")
        print(f"   Total API Calls: {monitoring_results['summary']['total_calls']}")
        print(f"   Success Rate: {monitoring_results['summary']['success_rate']}%")
        print(f"   Average Duration: {monitoring_results['summary']['average_duration']}s")
        print(f"   Circuit Breaker Events: {len(monitoring_results['circuit_breaker_events'])}")
        
        if monitoring_results['summary']['success_rate'] >= 80:
            print("✅ System health is GOOD")
        elif monitoring_results['summary']['success_rate'] >= 60:
            print("⚠️ System health is FAIR - monitor closely")
        else:
            print("🚨 System health is POOR - further investigation needed")
        
        return monitoring_results
        
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped by user")
        return None

def export_diagnostic_report():
    """Export a comprehensive diagnostic report."""
    print("\n📄 Exporting diagnostic report...")
    
    try:
        # Run final diagnostics
        diagnostics = api_diagnostics.run_comprehensive_diagnostic()
        
        # Export to file
        filename = api_diagnostics.export_diagnostics()
        
        if filename:
            print(f"✅ Diagnostic report exported to: {filename}")
            print("   This file contains detailed information about API performance and issues")
        else:
            print("❌ Failed to export diagnostic report")
        
        return filename
        
    except Exception as e:
        print(f"❌ Error exporting diagnostic report: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Starting AIFXHunter Pro v2.2 Rate Limiting Fix...")
    
    try:
        # Apply fixes
        improvement_metrics = fix_rate_limiting_issues()
        
        # Ask user if they want to monitor system health
        print("\n" + "=" * 60)
        monitor_choice = input("Would you like to monitor system health for 10 minutes? (y/n): ").lower().strip()
        
        if monitor_choice in ['y', 'yes']:
            monitoring_results = monitor_system_health(10)
        
        # Ask user if they want to export diagnostic report
        export_choice = input("Would you like to export a diagnostic report? (y/n): ").lower().strip()
        
        if export_choice in ['y', 'yes']:
            export_diagnostic_report()
        
        print("\n🎉 All operations completed successfully!")
        print("💡 You can now restart the AIFXHunter application with improved rate limiting.")
        
    except KeyboardInterrupt:
        print("\n⏹️ Operation cancelled by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        print("Please check the error and try again")
