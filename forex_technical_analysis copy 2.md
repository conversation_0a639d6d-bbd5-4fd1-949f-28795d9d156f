from tradingview_ta import TA_Handler, Interval
import streamlit as st
import time
from datetime import datetime, timedelta

# Initialize session state for signal tracking
if 'last_strong_signals' not in st.session_state:
    st.session_state.last_strong_signals = set()

# Rotational scan state management
if 'scan_index' not in st.session_state:
    st.session_state.scan_index = 0

if 'all_results' not in st.session_state:
    st.session_state.all_results = {
        "Strong Buy": [], "Weak Buy": [], "Neutral": [],
        "Weak Sell": [], "Strong Sell": []
    }

def get_tradingview_analysis(symbol, timeframe):
    """
    Get TradingView's official technical analysis for forex pairs.

    Args:
        symbol (str): Forex pair symbol (e.g., 'EURUSD=X' or 'EURUSD')
        timeframe (str): Time interval for analysis (e.g., '5m', '1h', '4h')

    Returns:
        tuple: (calculated_score, volatility_estimate, signals_list)
    """
    try:
        # Convert symbol format: "EURUSD=X" → "EURUSD"
        clean_symbol = symbol.replace('=X', '')

        # Map timeframes to TradingView intervals
        interval_map = {
            "5m": Interval.INTERVAL_5_MINUTES,
            "15m": Interval.INTERVAL_15_MINUTES,
            "30m": Interval.INTERVAL_30_MINUTES,
            "1h": Interval.INTERVAL_1_HOUR,
            "4h": Interval.INTERVAL_4_HOURS
        }

        tv_interval = interval_map.get(timeframe, Interval.INTERVAL_1_HOUR)

        # Create TradingView TA_Handler
        handler = TA_Handler(
            symbol=clean_symbol,
            screener="forex",
            exchange="FX_IDC",
            interval=tv_interval
        )

        # Get analysis from TradingView
        analysis = handler.get_analysis()

        # Calculate score: BUY signals - SELL signals (range approximately -26 to +26)
        buy_signals = analysis.summary['BUY']
        sell_signals = analysis.summary['SELL']
        neutral_signals = analysis.summary['NEUTRAL']

        # Calculate weighted score (multiply by 2 to get better range for our thresholds)
        raw_score = (buy_signals - sell_signals) * 2

        # Generate detailed signals list from individual indicators
        signals = []
        indicators = analysis.indicators

        # RSI Analysis
        if 'RSI' in indicators:
            rsi_value = indicators['RSI']
            if rsi_value <= 30:
                signals.append(f"TradingView RSI Oversold ({rsi_value:.1f})")
            elif rsi_value >= 70:
                signals.append(f"TradingView RSI Overbought ({rsi_value:.1f})")
            elif rsi_value <= 40:
                signals.append(f"TradingView RSI Bullish ({rsi_value:.1f})")
            elif rsi_value >= 60:
                signals.append(f"TradingView RSI Bearish ({rsi_value:.1f})")

        # Stochastic Analysis
        if 'Stoch.K' in indicators:
            stoch_k = indicators['Stoch.K']
            if stoch_k <= 20:
                signals.append(f"TradingView Stochastic Oversold ({stoch_k:.1f})")
            elif stoch_k >= 80:
                signals.append(f"TradingView Stochastic Overbought ({stoch_k:.1f})")

        # MACD Analysis
        if 'MACD.macd' in indicators and 'MACD.signal' in indicators:
            macd_line = indicators['MACD.macd']
            macd_signal = indicators['MACD.signal']
            if macd_line > macd_signal:
                signals.append("TradingView MACD Bullish Crossover")
            else:
                signals.append("TradingView MACD Bearish Crossover")

        # Moving Average Analysis
        if 'SMA20' in indicators and 'close' in indicators:
            sma20 = indicators['SMA20']
            close_price = indicators['close']
            if close_price > sma20:
                signals.append("TradingView Price Above SMA20")
            else:
                signals.append("TradingView Price Below SMA20")

        # Bollinger Bands Analysis
        if 'BB.upper' in indicators and 'BB.lower' in indicators and 'close' in indicators:
            bb_upper = indicators['BB.upper']
            bb_lower = indicators['BB.lower']
            close_price = indicators['close']
            if close_price <= bb_lower:
                signals.append("TradingView Price at Lower Bollinger Band")
            elif close_price >= bb_upper:
                signals.append("TradingView Price at Upper Bollinger Band")

        # Add summary information
        signals.append(f"TradingView Summary: {buy_signals} BUY, {neutral_signals} NEUTRAL, {sell_signals} SELL")

        # Estimate volatility (use a simple proxy based on signal distribution)
        total_signals = buy_signals + neutral_signals + sell_signals
        if total_signals > 0:
            volatility_estimate = abs(buy_signals - sell_signals) / total_signals * 0.1
        else:
            volatility_estimate = 0.02

        return raw_score, volatility_estimate, signals

    except Exception as e:
        print(f"Error getting TradingView analysis for {symbol}: {str(e)}")
        # Return fallback values to keep the app running
        return 0, 0.02, [f"TradingView analysis unavailable for {symbol.replace('=X', '')}"]

def determine_expiration(base_timeframe, score, volatility):
    """Enhanced expiration logic"""
    timeframe_minutes = {
        '5m': 5, '15m': 15, '30m': 30, '1h': 60, '4h': 240
    }
    base_minutes = timeframe_minutes.get(base_timeframe, 60)

    # More aggressive signal strength adjustment
    abs_score = abs(score)
    if abs_score > 80:  # Very strong signals
        strength_multiplier = 0.4
    elif abs_score > 60:  # Strong signals
        strength_multiplier = 0.7
    elif abs_score > 40:  # Medium signals
        strength_multiplier = 1.0
    else:  # Weak signals
        strength_multiplier = 1.3

    # Volatility adjustment
    if volatility > 0.06:  # Very high volatility
        volatility_multiplier = 0.6
    elif volatility > 0.04:  # High volatility
        volatility_multiplier = 0.8
    elif volatility >= 0.02:  # Normal volatility
        volatility_multiplier = 1.0
    else:  # Low volatility
        volatility_multiplier = 1.4

    final_minutes = base_minutes * strength_multiplier * volatility_multiplier

    if final_minutes >= 60:
        hours = final_minutes / 60
        if hours == int(hours):
            return f"{int(hours)} hour{'s' if hours != 1 else ''}"
        else:
            return f"{hours:.1f} hours"
    else:
        return f"{int(final_minutes)} minutes"

def get_tradingview_chart_html(symbol, timeframe):
    """
    Generate TradingView embedded chart HTML for forex pairs.

    Args:
        symbol (str): Forex pair symbol with yfinance suffix (e.g., 'EURUSD=X')
        timeframe (str): Time interval for data (e.g., '5m', '1h', '4h')

    Returns:
        str: Complete HTML string containing TradingView widget
    """
    try:
        # Convert yfinance format to TradingView format
        # "EURUSD=X" → "FX:EURUSD"
        tv_symbol = f"FX:{symbol.replace('=X', '')}"

        # Convert timeframe to TradingView intervals
        timeframe_map = {
            "5m": "5",
            "15m": "15",
            "30m": "30",
            "1h": "60",
            "4h": "240"
        }
        tv_interval = timeframe_map.get(timeframe, "60")

        # Generate unique container ID to avoid conflicts
        import random
        container_id = f"tradingview_{random.randint(1000, 9999)}"

        # Create TradingView widget HTML
        html_code = f"""
        <div id="{container_id}" style="height: 500px; width: 100%;"></div>
        <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
        <script type="text/javascript">
        new TradingView.widget({{
            "width": "100%",
            "height": 500,
            "symbol": "{tv_symbol}",
            "interval": "{tv_interval}",
            "timezone": "Etc/UTC",
            "theme": "dark",
            "style": "1",
            "locale": "en",
            "toolbar_bg": "#f1f3f6",
            "enable_publishing": false,
            "allow_symbol_change": true,
            "hide_side_toolbar": false,
            "container_id": "{container_id}"
        }});
        </script>
        """

        return html_code

    except Exception as e:
        print(f"Error creating TradingView chart for {symbol}: {str(e)}")
        return None

def analyze_multiple_pairs(symbols, timeframe):
    """Enhanced analysis with signal details"""
    categories = {
        "Strong Buy": [],
        "Weak Buy": [],
        "Neutral": [],
        "Weak Sell": [],
        "Strong Sell": []
    }
    
    for symbol in symbols:
        score, volatility, signals = get_tradingview_analysis(symbol, timeframe)
        if score is not None and volatility is not None:
            # --- NEW THRESHOLDS TUNED FOR THE AGGRESSIVE SCORING ---
            if score >= 50:  # A confluence of at least two strong signals
                categories["Strong Buy"].append((symbol, score, volatility, signals))
            elif score >= 20: # At least one clear signal
                categories["Weak Buy"].append((symbol, score, volatility, signals))
            elif score <= -50:
                categories["Strong Sell"].append((symbol, score, volatility, signals))
            elif score <= -20:
                categories["Weak Sell"].append((symbol, score, volatility, signals))
            else:
                categories["Neutral"].append((symbol, score, volatility, signals))
    
    # Sort by absolute score
    for category in categories:
        categories[category] = sorted(categories[category], key=lambda x: abs(x[1]), reverse=True)
        
    return categories

# Extended watchlist - Clean TradingView format
watchlist = [
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD",
    "EURJPY", "EURGBP", "EURAUD", "EURCAD", "EURCHF",
    "GBPJPY", "AUDJPY", "CADJPY", "CHFJPY",
    "GBPAUD", "GBPCAD", "GBPCHF",
    "AUDCAD", "AUDCHF", "AUDNZD", "CADCHF"
]

# Streamlit UI
st.set_page_config(page_title="Enhanced AI Forex Analysis", layout="wide")

# Custom CSS for better styling
st.markdown("""
<style>
.metric-card {
    background-color: #1e1e1e;
    padding: 10px;
    border-radius: 8px;
    margin: 5px 0;
    border-left: 4px solid #00ff88;
}
.signal-details {
    font-size: 0.8em;
    color: #888;
    margin-top: 5px;
}
</style>
""", unsafe_allow_html=True)

st.title("🚀 Enhanced AI Forex Analysis Bot")
st.markdown("*Real-time technical analysis with comprehensive indicators*")

# Timeframe selection
timeframe = st.selectbox(
    "Select Timeframe",
    options=["5m", "15m", "30m", "1h", "4h"],
    index=3
)

st.markdown("🎯 **Real-time Signal Hunter Mode** - Scanning for new Strong Buy/Sell signals every 10 seconds")

# Market overview section
with st.expander("📊 All Markets Being Analyzed (22 pairs)"):
    cols = st.columns(4)
    display_list = watchlist  # Already clean format
    chunk_size = (len(display_list) + len(cols) - 1) // len(cols)

    for i, col in enumerate(cols):
        chunk = display_list[i*chunk_size : (i+1)*chunk_size]
        with col:
            for pair in chunk:
                if st.button(pair, key=f"pair_{pair}"):
                    # Store in both formats for compatibility
                    st.session_state['selected_symbol'] = f"{pair}=X"
                    st.session_state['selected_timeframe'] = timeframe
                    st.rerun()

# Placeholders
alert_placeholder = st.empty()
header_placeholder = st.empty()
results_placeholder = st.empty()
status_placeholder = st.empty()

# =============================================================================
# CORRECTED AUTO-REFRESH IMPLEMENTATION
# =============================================================================

def update_dashboard():
    """Execute analysis and update all dashboard components"""
    current_time = datetime.now()

    # Update header with current time
    with header_placeholder.container():
        st.subheader(f"⏰ Current Time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if st.session_state.last_update:
            next_refresh = st.session_state.last_update + timedelta(seconds=60)
            time_until_refresh = max(0, int((next_refresh - current_time).total_seconds()))
            st.caption(f"Last updated: {st.session_state.last_update.strftime('%H:%M:%S')} | Next refresh in: {time_until_refresh} seconds")

    # Execute analysis with spinner
    with status_placeholder.container():
        with st.spinner("🔍 Analyzing 22 forex pairs with enhanced indicators..."):
            results = analyze_multiple_pairs(watchlist, timeframe)
            st.session_state.last_update = current_time
            display_results(results, timeframe, f"initial_{int(current_time.timestamp() * 1000)}")

        # Count and display active signals
        active_signals = sum(len(pairs) for category, pairs in results.items()
                           if category in ["Strong Buy", "Weak Buy", "Strong Sell", "Weak Sell"])

        if active_signals > 0:
            st.success(f"✅ Analysis complete! Found {active_signals} active trading signals.")
        else:
            st.info("📊 Analysis complete. No strong signals detected - market may be consolidating.")

# Session state management
if 'last_timeframe' not in st.session_state:
    st.session_state.last_timeframe = timeframe
if 'last_update' not in st.session_state:
    st.session_state.last_update = None

def display_results(results, timeframe, cycle_id=None):
    """Enhanced results display with signal details"""
    # Generate unique cycle identifier if not provided
    if cycle_id is None:
        cycle_id = int(datetime.now().timestamp() * 1000)  # Millisecond timestamp
    with results_placeholder.container():
        st.subheader(f"📈 Technical Analysis Results - {timeframe} Timeframe")
        
        # Summary statistics
        total_signals = sum(len(pairs) for pairs in results.values())
        strong_signals = len(results["Strong Buy"]) + len(results["Strong Sell"])
        
        col1, col2, col3 = st.columns(3)
        col1.metric("Total Pairs Analyzed", len(watchlist))
        col2.metric("Active Signals", total_signals)
        col3.metric("Strong Signals", strong_signals)
        
        st.markdown("---")

        # Results display
        cols = st.columns(5)
        
        category_colors = {
            "Strong Buy": "🟢", "Weak Buy": "🔵", "Neutral": "⚪", 
            "Weak Sell": "🟠", "Strong Sell": "🔴"
        }

        for i, (category, pairs) in enumerate(results.items()):
            with cols[i]:
                st.markdown(f"### {category_colors[category]} {category}")
                
                if not pairs:
                    st.write("No signals")
                else:
                    for j, (symbol, score, volatility, signals) in enumerate(pairs):
                        # --- NEW: Detailed, Transparent Signal Card ---
                        symbol_name = symbol  # Already clean format
                        expiration_time = determine_expiration(timeframe, score, volatility)

                        # Use an expander for each signal to show details
                        with st.expander(f"{symbol_name} (Score: {score:.1f})"):
                            # Display the main signal recommendation
                            if score > 0:
                                st.success(f"**Recommendation: BUY - Expiry: {expiration_time}**")
                            else:
                                st.error(f"**Recommendation: SELL - Expiry: {expiration_time}**")

                            st.markdown("---")

                            # List all the technical reasons for the signal
                            st.write("**Technical Reasons:**")
                            if signals:
                                for reason in signals:
                                    st.markdown(f"- ✅ {reason}")
                            else:
                                st.write("No specific conditions met for this score.")

                            st.write(f"_(Volatility Index: {volatility:.4f})_")

                            # Add the chart button inside the expander
                            if st.button("📊 Show Chart", key=f"chart_{symbol}_{category}_{j}_{cycle_id}"):
                                # Store with =X suffix for TradingView chart compatibility
                                st.session_state['selected_symbol'] = f"{symbol}=X"
                                st.session_state['selected_timeframe'] = timeframe
                                st.rerun()

# Chart display
if 'selected_symbol' in st.session_state and 'selected_timeframe' in st.session_state:
    st.markdown("---")
    st.subheader(f"📊 Enhanced Chart - {st.session_state['selected_symbol'].replace('=X', '')} ({st.session_state['selected_timeframe']})")

    chart_col, button_col = st.columns([5, 1])
    
    with button_col:
        if st.button("🗑️ Clear Chart"):
            del st.session_state['selected_symbol']
            del st.session_state['selected_timeframe']
            st.rerun()

    with chart_col:
        with st.spinner("Loading professional TradingView chart..."):
            html_code = get_tradingview_chart_html(st.session_state['selected_symbol'], st.session_state['selected_timeframe'])
            if html_code:
                st.components.v1.html(html_code, height=500)
            else:
                st.error("Unable to load TradingView chart. Please try again.")

# Execute initial analysis immediately
update_dashboard()

# =============================================================================
# REAL-TIME SIGNAL HUNTER MAIN LOOP
# =============================================================================

def play_alert_sound():
    """Play a beep sound for new strong signals"""
    sound_html = """
    <script>
    // Create audio context for beep sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = 800; // 800 Hz beep
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
    </script>
    """
    st.components.v1.html(sound_html, height=0)

# =============================================================================
# ROTATIONAL SIGNAL HUNTER - API-FRIENDLY IMPLEMENTATION
# =============================================================================

# Configuration
SCAN_CHUNK_SIZE = 4  # Analyze 4 pairs per cycle (reduces API calls by 82%)
current_time = datetime.now()

# Calculate which pairs to scan this cycle
start_index = st.session_state.scan_index
end_index = start_index + SCAN_CHUNK_SIZE
pairs_to_scan = watchlist[start_index:end_index]

# Handle wraparound if we reach the end of the watchlist
if end_index > len(watchlist):
    remaining_count = end_index - len(watchlist)
    pairs_to_scan.extend(watchlist[0:remaining_count])

# Analyze only the current chunk of pairs
for symbol in pairs_to_scan:
    score, volatility, signals = get_tradingview_analysis(symbol, timeframe)

    if score is not None and volatility is not None:
        # Remove any existing entry for this symbol from all categories
        for category in st.session_state.all_results:
            st.session_state.all_results[category] = [
                item for item in st.session_state.all_results[category]
                if item[0] != symbol
            ]

        # Add updated signal to appropriate category using existing thresholds
        if score >= 50:
            st.session_state.all_results["Strong Buy"].append((symbol, score, volatility, signals))
        elif score >= 20:
            st.session_state.all_results["Weak Buy"].append((symbol, score, volatility, signals))
        elif score <= -50:
            st.session_state.all_results["Strong Sell"].append((symbol, score, volatility, signals))
        elif score <= -20:
            st.session_state.all_results["Weak Sell"].append((symbol, score, volatility, signals))
        else:
            st.session_state.all_results["Neutral"].append((symbol, score, volatility, signals))

# Update scan position for next cycle
st.session_state.scan_index = end_index % len(watchlist)

# Sort all categories by absolute score (maintain existing behavior)
for category in st.session_state.all_results:
    st.session_state.all_results[category] = sorted(
        st.session_state.all_results[category],
        key=lambda x: abs(x[1]), reverse=True
    )

# Extract current strong signals for alert system
current_strong_signals = set()
for symbol, score, volatility, signals in st.session_state.all_results["Strong Buy"]:
    current_strong_signals.add(f"STRONG BUY: {symbol}")
for symbol, score, volatility, signals in st.session_state.all_results["Strong Sell"]:
    current_strong_signals.add(f"STRONG SELL: {symbol}")

# Detect and display new strong signals
new_strong_signals = current_strong_signals - st.session_state.last_strong_signals

if new_strong_signals:
    with alert_placeholder.container():
        st.markdown("### 🚨 NEW STRONG SIGNAL DETECTED! 🚨")
        for signal in new_strong_signals:
            if "BUY" in signal:
                st.success(f"🟢 {signal} - {current_time.strftime('%H:%M:%S')}")
            else:
                st.error(f"🔴 {signal} - {current_time.strftime('%H:%M:%S')}")

        # Play alert sound
        play_alert_sound()
        time.sleep(2)
else:
    alert_placeholder.empty()

# Update session state
st.session_state.last_strong_signals = current_strong_signals

# Update dashboard header
with header_placeholder.container():
    cycle_progress = f"{min(st.session_state.scan_index, len(watchlist))}/{len(watchlist)}"
    st.subheader(f"🎯 Rotational Scan Active - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    st.caption(f"Scanning 4 pairs every 10 seconds | Cycle progress: {cycle_progress} | Strong signals: {len(current_strong_signals)}")

# Display complete results using accumulated data
with status_placeholder.container():
    st.session_state.last_update = current_time
    display_results(st.session_state.all_results, timeframe, f"rotational_{int(current_time.timestamp() * 1000)}")

    # Show scan statistics
    total_signals = sum(len(pairs) for pairs in st.session_state.all_results.values())
    strong_signals = len(st.session_state.all_results["Strong Buy"]) + len(st.session_state.all_results["Strong Sell"])

    if strong_signals > 0:
        st.info(f"🎯 Rotational Hunter: {strong_signals} strong signals active | {len(new_strong_signals)} new this cycle")
    else:
        st.info("🎯 Rotational Hunter: No strong signals detected - continuing rotational scan...")

# Sleep and restart cycle
time.sleep(10)
st.rerun()
