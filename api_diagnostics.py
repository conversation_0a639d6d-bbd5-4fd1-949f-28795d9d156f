#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIFXHunter Pro v2.2 - API Diagnostics and Rate Limiting Monitor
Comprehensive monitoring and diagnostic tools for TradingView API issues
"""

import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from enhanced_price_manager import price_manager
from performance_database import PerformanceDatabase

class APIDiagnostics:
    """Comprehensive API diagnostics and monitoring system."""
    
    def __init__(self):
        self.diagnostic_history = []
        self.rate_limit_events = []
        self.failed_requests = []
        self.circuit_breaker_events = []
        self.performance_metrics = {}
        
    def run_comprehensive_diagnostic(self) -> Dict[str, Any]:
        """Run a comprehensive diagnostic of the API and signal evaluation system."""
        print("🔍 Running comprehensive API diagnostics...")
        
        diagnostic_results = {
            'timestamp': datetime.now().isoformat(),
            'price_manager_status': self._diagnose_price_manager(),
            'signal_evaluation_status': self._diagnose_signal_evaluation(),
            'rate_limiting_analysis': self._analyze_rate_limiting(),
            'circuit_breaker_status': self._check_circuit_breaker(),
            'cache_analysis': self._analyze_cache_performance(),
            'recommendations': self._generate_recommendations()
        }
        
        self.diagnostic_history.append(diagnostic_results)
        return diagnostic_results
    
    def _diagnose_price_manager(self) -> Dict[str, Any]:
        """Diagnose the price manager status and performance."""
        try:
            status = price_manager.get_status()
            
            # Test price fetching for a few common pairs
            test_pairs = ['EURUSD', 'GBPUSD', 'USDJPY']
            price_test_results = {}
            
            for pair in test_pairs:
                start_time = time.time()
                price = price_manager.get_current_price(pair)
                fetch_time = time.time() - start_time
                
                price_test_results[pair] = {
                    'price': price,
                    'fetch_time_seconds': round(fetch_time, 3),
                    'success': price is not None
                }
            
            return {
                'status': status,
                'price_tests': price_test_results,
                'overall_health': 'healthy' if status['can_make_api_call'] else 'degraded'
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'overall_health': 'error'
            }
    
    def _diagnose_signal_evaluation(self) -> Dict[str, Any]:
        """Diagnose signal evaluation system performance."""
        try:
            # Check database for pending signals
            db = PerformanceDatabase()
            pending_signals = db.get_pending_signals()
            
            # Analyze pending signals
            pending_analysis = {
                'total_pending': len(pending_signals),
                'by_asset': {},
                'age_distribution': {
                    'under_5_min': 0,
                    '5_to_30_min': 0,
                    'over_30_min': 0
                }
            }
            
            now = datetime.now()
            for signal in pending_signals:
                asset = signal.get('asset', 'unknown')
                expiry_time = signal.get('expiry_time')
                
                # Count by asset
                pending_analysis['by_asset'][asset] = pending_analysis['by_asset'].get(asset, 0) + 1
                
                # Analyze age
                if expiry_time:
                    try:
                        if isinstance(expiry_time, str):
                            expiry_dt = datetime.fromisoformat(expiry_time)
                        else:
                            expiry_dt = expiry_time
                        
                        age_minutes = (now - expiry_dt).total_seconds() / 60
                        
                        if age_minutes < 5:
                            pending_analysis['age_distribution']['under_5_min'] += 1
                        elif age_minutes < 30:
                            pending_analysis['age_distribution']['5_to_30_min'] += 1
                        else:
                            pending_analysis['age_distribution']['over_30_min'] += 1
                    except:
                        pass
            
            # Check for failed signals
            failed_signals_count = len(price_manager.failed_signals)
            
            return {
                'pending_signals': pending_analysis,
                'failed_signals_count': failed_signals_count,
                'evaluation_health': 'healthy' if pending_analysis['age_distribution']['over_30_min'] < 5 else 'degraded'
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'evaluation_health': 'error'
            }
    
    def _analyze_rate_limiting(self) -> Dict[str, Any]:
        """Analyze rate limiting patterns and issues."""
        try:
            status = price_manager.get_status()
            
            # Calculate rate limiting metrics
            api_calls_last_minute = status.get('api_calls_last_minute', 0)
            max_calls_per_minute = price_manager.max_calls_per_minute
            
            rate_usage_percent = (api_calls_last_minute / max_calls_per_minute) * 100
            
            # Determine rate limiting health
            if rate_usage_percent < 50:
                rate_health = 'healthy'
            elif rate_usage_percent < 80:
                rate_health = 'warning'
            else:
                rate_health = 'critical'
            
            return {
                'api_calls_last_minute': api_calls_last_minute,
                'max_calls_per_minute': max_calls_per_minute,
                'usage_percentage': round(rate_usage_percent, 1),
                'rate_health': rate_health,
                'can_make_api_call': status.get('can_make_api_call', False),
                'last_api_call': status.get('last_api_call'),
                'consecutive_failures': status.get('consecutive_failures', 0)
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'rate_health': 'error'
            }
    
    def _check_circuit_breaker(self) -> Dict[str, Any]:
        """Check circuit breaker status and history."""
        try:
            status = price_manager.get_status()
            
            circuit_breaker_active = status.get('circuit_breaker_active', False)
            circuit_breaker_until = status.get('circuit_breaker_until')
            
            if circuit_breaker_active and circuit_breaker_until:
                remaining_time = datetime.fromisoformat(circuit_breaker_until) - datetime.now()
                remaining_seconds = max(0, remaining_time.total_seconds())
            else:
                remaining_seconds = 0
            
            return {
                'active': circuit_breaker_active,
                'until': circuit_breaker_until,
                'remaining_seconds': round(remaining_seconds),
                'consecutive_failures': status.get('consecutive_failures', 0),
                'status': 'active' if circuit_breaker_active else 'inactive'
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'status': 'error'
            }
    
    def _analyze_cache_performance(self) -> Dict[str, Any]:
        """Analyze cache performance and efficiency."""
        try:
            status = price_manager.get_status()
            cached_prices = status.get('cached_prices', 0)
            
            # Estimate cache hit rate (simplified)
            cache_efficiency = 'good' if cached_prices > 0 else 'low'
            
            return {
                'cached_prices': cached_prices,
                'cache_efficiency': cache_efficiency,
                'cache_duration_minutes': price_manager.cache_duration.total_seconds() / 60
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'cache_efficiency': 'error'
            }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on diagnostic results."""
        recommendations = []
        
        try:
            status = price_manager.get_status()
            
            # Circuit breaker recommendations
            if status.get('circuit_breaker_active'):
                recommendations.append("🚨 Circuit breaker is active - wait for it to reset before making API calls")
            
            # Rate limiting recommendations
            api_calls = status.get('api_calls_last_minute', 0)
            if api_calls > price_manager.max_calls_per_minute * 0.8:
                recommendations.append("⚠️ API usage is high - consider increasing delays between calls")
            
            # Failed signals recommendations
            if len(price_manager.failed_signals) > 10:
                recommendations.append("🔄 Many signals have failed evaluation - consider clearing failed signals list")
            
            # Cache recommendations
            if status.get('cached_prices', 0) == 0:
                recommendations.append("📋 No cached prices - cache is not being utilized effectively")
            
            # General recommendations
            recommendations.extend([
                "💡 Use cached prices when possible to reduce API calls",
                "⏱️ Implement exponential backoff for failed requests",
                "🔄 Consider alternative data sources for price information",
                "📊 Monitor API usage patterns to optimize call frequency"
            ])
            
        except Exception as e:
            recommendations.append(f"❌ Error generating recommendations: {e}")
        
        return recommendations
    
    def monitor_api_health(self, duration_minutes: int = 5) -> Dict[str, Any]:
        """Monitor API health over a specified duration."""
        print(f"📊 Monitoring API health for {duration_minutes} minutes...")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        monitoring_results = {
            'start_time': start_time.isoformat(),
            'duration_minutes': duration_minutes,
            'api_calls': [],
            'errors': [],
            'rate_limit_events': [],
            'circuit_breaker_events': []
        }
        
        # Test API calls periodically
        test_interval = 30  # Test every 30 seconds
        next_test = start_time
        
        while datetime.now() < end_time:
            if datetime.now() >= next_test:
                # Test API call
                test_start = time.time()
                price = price_manager.get_current_price('EURUSD')
                test_duration = time.time() - test_start
                
                call_result = {
                    'timestamp': datetime.now().isoformat(),
                    'success': price is not None,
                    'duration_seconds': round(test_duration, 3),
                    'price': price
                }
                
                monitoring_results['api_calls'].append(call_result)
                
                # Check for rate limiting or circuit breaker
                status = price_manager.get_status()
                if status.get('circuit_breaker_active'):
                    monitoring_results['circuit_breaker_events'].append({
                        'timestamp': datetime.now().isoformat(),
                        'event': 'circuit_breaker_active'
                    })
                
                next_test = datetime.now() + timedelta(seconds=test_interval)
            
            time.sleep(1)  # Check every second
        
        # Calculate summary statistics
        successful_calls = sum(1 for call in monitoring_results['api_calls'] if call['success'])
        total_calls = len(monitoring_results['api_calls'])
        
        monitoring_results['summary'] = {
            'total_calls': total_calls,
            'successful_calls': successful_calls,
            'success_rate': round((successful_calls / total_calls * 100) if total_calls > 0 else 0, 1),
            'average_duration': round(
                sum(call['duration_seconds'] for call in monitoring_results['api_calls']) / total_calls
                if total_calls > 0 else 0, 3
            )
        }
        
        return monitoring_results
    
    def export_diagnostics(self, filename: str = None) -> str:
        """Export diagnostic results to a JSON file."""
        if filename is None:
            filename = f"api_diagnostics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            diagnostic_data = {
                'export_timestamp': datetime.now().isoformat(),
                'diagnostic_history': self.diagnostic_history,
                'rate_limit_events': self.rate_limit_events,
                'failed_requests': self.failed_requests,
                'circuit_breaker_events': self.circuit_breaker_events
            }
            
            with open(filename, 'w') as f:
                json.dump(diagnostic_data, f, indent=2, default=str)
            
            print(f"✅ Diagnostics exported to {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Error exporting diagnostics: {e}")
            return ""

# Global diagnostics instance
api_diagnostics = APIDiagnostics()
