#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIFXHunter Pro v2.2 - Countdown Timer Demo
Standalone demo of the visual countdown timers and progress bars
"""

import streamlit as st
import time
from datetime import datetime, timedelta
import sqlite3
from performance_database import PerformanceDatabase

def main():
    st.set_page_config(
        page_title="AIFXHunter Pro v2.2 - Countdown Timer Demo",
        page_icon="⏰",
        layout="wide"
    )
    
    st.title("⏰ AIFXHunter Pro v2.2 - Signal Countdown Timer Demo")
    st.markdown("**Real-time countdown timers and progress bars for pending binary options signals**")
    
    # Initialize database
    if 'database' not in st.session_state:
        st.session_state.database = PerformanceDatabase()
    
    # Auto-refresh every 1 second
    if 'last_update' not in st.session_state:
        st.session_state.last_update = time.time()
    
    # Force refresh every second
    current_time = time.time()
    if current_time - st.session_state.last_update >= 1:
        st.session_state.last_update = current_time
        st.rerun()
    
    # Get pending signals
    try:
        with sqlite3.connect(st.session_state.database.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
            SELECT s.signal_id, s.asset, s.signal_type, s.entry_price, 
                   s.expiry_time, s.confidence, s.strategy_name, s.timestamp
            FROM signals s
            LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
            WHERE sr.signal_id IS NULL
            ORDER BY s.expiry_time ASC
            LIMIT 10
            """)
            
            pending_signals = cursor.fetchall()
            
            if pending_signals:
                st.markdown("### ⏳ Pending Signals with Live Countdown")
                
                for signal in pending_signals:
                    signal_id, asset, signal_type, entry_price, expiry_time, confidence, strategy_name, timestamp = signal
                    
                    try:
                        expiry_dt = datetime.fromisoformat(expiry_time)
                        signal_dt = datetime.fromisoformat(timestamp)
                        now = datetime.now()
                        
                        time_remaining = expiry_dt - now
                        total_duration = expiry_dt - signal_dt
                        
                        if time_remaining.total_seconds() > 0:
                            # Calculate time components
                            minutes_left = int(time_remaining.total_seconds() / 60)
                            seconds_left = int(time_remaining.total_seconds() % 60)
                            
                            # Determine color and urgency
                            if time_remaining.total_seconds() > 5 * 60:  # > 5 minutes
                                color = "#10b981"  # Green
                                urgency = "Normal"
                                icon = "⏳"
                            elif time_remaining.total_seconds() > 2 * 60:  # 2-5 minutes
                                color = "#f59e0b"  # Yellow
                                urgency = "Warning"
                                icon = "⚠️"
                            else:  # < 2 minutes
                                color = "#ef4444"  # Red
                                urgency = "Urgent"
                                icon = "🚨" if time_remaining.total_seconds() < 30 else "🔴"
                            
                            # Calculate progress
                            elapsed_time = total_duration.total_seconds() - time_remaining.total_seconds()
                            progress_percent = (elapsed_time / total_duration.total_seconds()) * 100 if total_duration.total_seconds() > 0 else 0
                            
                            # Create columns for layout
                            col1, col2, col3 = st.columns([2, 1, 1])
                            
                            with col1:
                                st.markdown(f"""
                                <div style="background: linear-gradient(135deg, {color}20, {color}10); padding: 1rem; border-radius: 8px; border: 2px solid {color}40;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                        <div style="color: {color}; font-weight: 700; font-size: 1.1rem;">
                                            {icon} {asset} • {signal_type}
                                        </div>
                                        <div style="color: {color}; font-size: 0.9rem; font-weight: 600;">
                                            {minutes_left}m {seconds_left}s
                                        </div>
                                    </div>
                                    
                                    <!-- Progress Bar -->
                                    <div style="background-color: rgba(255, 255, 255, 0.2); border-radius: 10px; height: 12px; overflow: hidden; margin: 0.5rem 0;">
                                        <div style="height: 100%; width: {progress_percent:.1f}%; background: linear-gradient(90deg, {color}, {color}80); border-radius: 10px; transition: width 0.3s ease;"></div>
                                    </div>
                                    
                                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.5rem; margin-top: 0.5rem;">
                                        <div style="text-align: center;">
                                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Entry</div>
                                            <div style="color: #3b82f6; font-size: 0.8rem; font-weight: 600;">{entry_price:.5f}</div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Confidence</div>
                                            <div style="color: #8b5cf6; font-size: 0.8rem; font-weight: 600;">{confidence:.0f}%</div>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="color: rgba(255,255,255,0.8); font-size: 0.7rem;">Urgency</div>
                                            <div style="color: {color}; font-size: 0.8rem; font-weight: 600;">{urgency}</div>
                                        </div>
                                    </div>
                                    
                                    <div style="color: rgba(255,255,255,0.7); font-size: 0.7rem; text-align: center; margin-top: 0.5rem;">
                                        Strategy: {strategy_name[:25]}{'...' if len(strategy_name) > 25 else ''}
                                    </div>
                                </div>
                                """, unsafe_allow_html=True)
                            
                            with col2:
                                # Progress indicator
                                st.metric(
                                    label="Progress",
                                    value=f"{progress_percent:.1f}%",
                                    delta=f"{urgency} Priority"
                                )
                            
                            with col3:
                                # Time remaining
                                st.metric(
                                    label="Time Left",
                                    value=f"{minutes_left}m {seconds_left}s",
                                    delta=f"Expires {expiry_dt.strftime('%H:%M:%S')}"
                                )
                            
                            # Add urgent notification for signals < 30 seconds
                            if time_remaining.total_seconds() < 30:
                                st.error(f"🚨 **URGENT**: {asset} {signal_type} signal expires in {seconds_left} seconds!")
                            elif time_remaining.total_seconds() < 2 * 60:
                                st.warning(f"⚠️ **WARNING**: {asset} {signal_type} signal expires in {minutes_left}m {seconds_left}s")
                            
                            st.markdown("---")
                            
                        else:
                            # Expired signal
                            st.markdown(f"""
                            <div style="background: rgba(107, 114, 128, 0.2); padding: 1rem; border-radius: 8px; border: 2px solid #6b728040; opacity: 0.7;">
                                <div style="color: #6b7280; font-weight: 700; font-size: 1.1rem;">
                                    ⏰ {asset} • {signal_type} • EXPIRED
                                </div>
                                <div style="color: #ef4444; font-size: 0.9rem; margin-top: 0.5rem;">
                                    Signal expired and is being evaluated...
                                </div>
                            </div>
                            """, unsafe_allow_html=True)
                            st.markdown("---")
                    
                    except Exception as e:
                        st.error(f"Error processing signal {signal_id}: {e}")
                
                # Summary statistics
                st.markdown("### 📊 Countdown Summary")
                
                # Count signals by urgency
                urgent_count = 0
                warning_count = 0
                normal_count = 0
                
                for signal in pending_signals:
                    try:
                        expiry_dt = datetime.fromisoformat(signal[4])
                        time_remaining = expiry_dt - datetime.now()
                        
                        if time_remaining.total_seconds() > 0:
                            if time_remaining.total_seconds() < 2 * 60:
                                urgent_count += 1
                            elif time_remaining.total_seconds() < 5 * 60:
                                warning_count += 1
                            else:
                                normal_count += 1
                    except:
                        pass
                
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("🚨 Urgent", urgent_count, "< 2 minutes")
                
                with col2:
                    st.metric("⚠️ Warning", warning_count, "2-5 minutes")
                
                with col3:
                    st.metric("⏳ Normal", normal_count, "> 5 minutes")
                
                with col4:
                    st.metric("📊 Total", len(pending_signals), "pending signals")
                
            else:
                st.info("⏳ No pending signals found. Generate some test signals to see the countdown timers!")
                
                if st.button("🎯 Generate Test Signals"):
                    # Generate quick test signals
                    import subprocess
                    try:
                        subprocess.run(["python", "generate_pending_signals.py", "quick"], check=True)
                        st.success("✅ Test signals generated! Refresh to see countdown timers.")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Error generating test signals: {e}")
    
    except Exception as e:
        st.error(f"Error loading pending signals: {e}")
    
    # Auto-refresh indicator
    st.markdown("---")
    st.markdown(f"🔄 **Auto-refresh active** - Last updated: {datetime.now().strftime('%H:%M:%S')}")
    st.markdown("💡 **Tip**: Countdown timers update every second automatically")
    
    # Feature showcase
    with st.expander("🎯 Countdown Timer Features"):
        st.markdown("""
        **✅ Real-time Features Implemented:**
        
        🕐 **Live Countdown Timers**
        - Updates every second automatically
        - Shows minutes and seconds remaining
        - Precise timing until signal expiry
        
        📊 **Visual Progress Bars**
        - Shows elapsed vs remaining time
        - Smooth progress animation
        - Color-coded urgency levels
        
        🎨 **Color-coded Urgency**
        - 🟢 **Green**: > 5 minutes remaining (Normal)
        - 🟡 **Yellow**: 2-5 minutes remaining (Warning)  
        - 🔴 **Red**: < 2 minutes remaining (Urgent)
        
        🚨 **Expiry Notifications**
        - Urgent alerts for signals < 30 seconds
        - Warning notifications for signals < 2 minutes
        - Visual indicators and status updates
        
        🔄 **Auto-refresh System**
        - Automatic page refresh every second
        - Real-time data updates
        - Seamless user experience
        
        📈 **Performance Metrics**
        - Signal count by urgency level
        - Total pending signals
        - Real-time statistics
        """)

if __name__ == "__main__":
    main()
