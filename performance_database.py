# -*- coding: utf-8 -*-
# =============================================================================
# AIFXHunter Pro v2.2 - Performance Database System
# Persistent storage for signal tracking and performance analytics
# =============================================================================

import sqlite3
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import os
from pathlib import Path

class PerformanceDatabase:
    """
    Persistent database system for storing and analyzing signal performance data.
    Uses SQLite for reliable local storage with comprehensive analytics capabilities.
    """
    
    def __init__(self, db_path: str = "aifxhunter_performance.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Signals table - stores all generated signals
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    signal_id TEXT UNIQUE NOT NULL,
                    timestamp DATETIME NOT NULL,
                    asset TEXT NOT NULL,
                    signal_type TEXT NOT NULL,  -- CALL or PUT
                    entry_price REAL NOT NULL,
                    expiry_time DATETIME NOT NULL,
                    expiry_minutes INTEGER NOT NULL,
                    confidence REAL NOT NULL,
                    strategy_name TEXT NOT NULL,
                    strategy_text TEXT,
                    timeframe TEXT NOT NULL,
                    risk_level TEXT NOT NULL,
                    market_conditions TEXT,  -- JSON string
                    reasoning TEXT,  -- JSON array of reasons
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # Signal results table - stores outcomes
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS signal_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    signal_id TEXT NOT NULL,
                    exit_price REAL NOT NULL,
                    result TEXT NOT NULL,  -- WIN, LOSS, TIE
                    profit_loss REAL,  -- Percentage gain/loss
                    evaluated_at DATETIME NOT NULL,
                    evaluation_method TEXT,  -- AUTO, MANUAL
                    notes TEXT,
                    FOREIGN KEY (signal_id) REFERENCES signals (signal_id)
                )
                """)
                
                # Strategy performance table - aggregated stats
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    total_signals INTEGER DEFAULT 0,
                    wins INTEGER DEFAULT 0,
                    losses INTEGER DEFAULT 0,
                    ties INTEGER DEFAULT 0,
                    win_rate REAL DEFAULT 0.0,
                    avg_confidence REAL DEFAULT 0.0,
                    best_pairs TEXT,  -- JSON array
                    best_timeframes TEXT,  -- JSON array
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(strategy_name)
                )
                """)
                
                # Market conditions performance
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS market_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    condition_type TEXT NOT NULL,  -- trend, volatility, momentum
                    condition_value TEXT NOT NULL,  -- bullish, high, etc.
                    total_signals INTEGER DEFAULT 0,
                    wins INTEGER DEFAULT 0,
                    win_rate REAL DEFAULT 0.0,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(condition_type, condition_value)
                )
                """)
                
                # Performance snapshots for historical tracking
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS performance_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    snapshot_date DATE NOT NULL,
                    total_signals INTEGER NOT NULL,
                    total_wins INTEGER NOT NULL,
                    overall_win_rate REAL NOT NULL,
                    best_strategy TEXT,
                    best_strategy_win_rate REAL,
                    worst_strategy TEXT,
                    worst_strategy_win_rate REAL,
                    avg_confidence REAL,
                    snapshot_data TEXT,  -- JSON with detailed metrics
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # Create indexes for better performance
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_signals_timestamp ON signals(timestamp)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_signals_asset ON signals(asset)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_signals_strategy ON signals(strategy_name)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_results_signal_id ON signal_results(signal_id)")
                
                conn.commit()
                print("✅ Performance database initialized successfully")
                
        except Exception as e:
            print(f"❌ Error initializing database: {e}")
    
    def store_signal(self, signal_data: Dict[str, Any]) -> bool:
        """Store a new signal in the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Parse expiry time
                expiry_time = datetime.now() + timedelta(minutes=int(signal_data.get('expiry', '5m').rstrip('m')))
                
                cursor.execute("""
                INSERT OR REPLACE INTO signals (
                    signal_id, timestamp, asset, signal_type, entry_price,
                    expiry_time, expiry_minutes, confidence, strategy_name,
                    strategy_text, timeframe, risk_level, market_conditions,
                    reasoning
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    signal_data.get('signal_id', f"SIG_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                    datetime.fromisoformat(signal_data.get('timestamp', datetime.now().isoformat())),
                    signal_data.get('asset'),
                    signal_data.get('signal'),
                    signal_data.get('entry_price'),
                    expiry_time,
                    int(signal_data.get('expiry', '5m').rstrip('m')),
                    float(signal_data.get('confidence', '0%').rstrip('%')),
                    signal_data.get('strategy_name'),
                    signal_data.get('strategy_text'),
                    signal_data.get('timeframe'),
                    signal_data.get('risk_level'),
                    json.dumps(signal_data.get('market_conditions', {})),
                    json.dumps(signal_data.get('reasoning', []))
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error storing signal: {e}")
            return False
    
    def store_signal_result(self, signal_id: str, exit_price: float, result: str, 
                           profit_loss: float = 0.0, evaluation_method: str = "AUTO") -> bool:
        """Store the result of a signal."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                INSERT INTO signal_results (
                    signal_id, exit_price, result, profit_loss,
                    evaluated_at, evaluation_method
                ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    signal_id,
                    exit_price,
                    result,
                    profit_loss,
                    datetime.now(),
                    evaluation_method
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error storing signal result: {e}")
            return False
    
    def get_pending_signals(self) -> List[Dict[str, Any]]:
        """Get signals that are waiting for evaluation."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                SELECT s.* FROM signals s
                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE sr.signal_id IS NULL
                AND s.expiry_time <= ?
                ORDER BY s.expiry_time
                """, (datetime.now(),))
                
                columns = [desc[0] for desc in cursor.description]
                results = []
                
                for row in cursor.fetchall():
                    signal_dict = dict(zip(columns, row))
                    # Parse JSON fields
                    signal_dict['market_conditions'] = json.loads(signal_dict.get('market_conditions', '{}'))
                    signal_dict['reasoning'] = json.loads(signal_dict.get('reasoning', '[]'))
                    results.append(signal_dict)
                
                return results
                
        except Exception as e:
            print(f"Error getting pending signals: {e}")
            return []
    
    def get_performance_stats(self, days: int = 30) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # Overall statistics
                cursor.execute("""
                SELECT 
                    COUNT(s.id) as total_signals,
                    COUNT(sr.id) as evaluated_signals,
                    SUM(CASE WHEN sr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    SUM(CASE WHEN sr.result = 'LOSS' THEN 1 ELSE 0 END) as losses,
                    SUM(CASE WHEN sr.result = 'TIE' THEN 1 ELSE 0 END) as ties,
                    AVG(s.confidence) as avg_confidence,
                    AVG(sr.profit_loss) as avg_profit_loss
                FROM signals s
                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE s.timestamp >= ?
                """, (cutoff_date,))
                
                overall_stats = dict(zip([desc[0] for desc in cursor.description], cursor.fetchone()))
                
                # Calculate win rate
                evaluated = overall_stats['evaluated_signals'] or 0
                wins = overall_stats['wins'] or 0
                overall_stats['win_rate'] = (wins / evaluated * 100) if evaluated > 0 else 0
                
                # Strategy performance
                cursor.execute("""
                SELECT 
                    s.strategy_name,
                    COUNT(s.id) as total_signals,
                    SUM(CASE WHEN sr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    COUNT(sr.id) as evaluated,
                    AVG(s.confidence) as avg_confidence
                FROM signals s
                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE s.timestamp >= ?
                GROUP BY s.strategy_name
                ORDER BY wins DESC
                """, (cutoff_date,))
                
                strategy_stats = []
                for row in cursor.fetchall():
                    strategy_dict = dict(zip([desc[0] for desc in cursor.description], row))
                    evaluated = strategy_dict['evaluated'] or 0
                    wins = strategy_dict['wins'] or 0
                    strategy_dict['win_rate'] = (wins / evaluated * 100) if evaluated > 0 else 0
                    strategy_stats.append(strategy_dict)
                
                # Asset performance
                cursor.execute("""
                SELECT 
                    s.asset,
                    COUNT(s.id) as total_signals,
                    SUM(CASE WHEN sr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    COUNT(sr.id) as evaluated
                FROM signals s
                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE s.timestamp >= ?
                GROUP BY s.asset
                ORDER BY wins DESC
                """, (cutoff_date,))
                
                asset_stats = []
                for row in cursor.fetchall():
                    asset_dict = dict(zip([desc[0] for desc in cursor.description], row))
                    evaluated = asset_dict['evaluated'] or 0
                    wins = asset_dict['wins'] or 0
                    asset_dict['win_rate'] = (wins / evaluated * 100) if evaluated > 0 else 0
                    asset_stats.append(asset_dict)
                
                return {
                    'overall': overall_stats,
                    'by_strategy': strategy_stats,
                    'by_asset': asset_stats,
                    'period_days': days
                }
                
        except Exception as e:
            print(f"Error getting performance stats: {e}")
            return {}
    
    def get_rolling_performance(self, hours: int = 24) -> Dict[str, float]:
        """Get rolling performance metrics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_time = datetime.now() - timedelta(hours=hours)
                
                cursor.execute("""
                SELECT 
                    COUNT(sr.id) as evaluated_signals,
                    SUM(CASE WHEN sr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    AVG(s.confidence) as avg_confidence
                FROM signals s
                JOIN signal_results sr ON s.signal_id = sr.signal_id
                WHERE s.timestamp >= ?
                """, (cutoff_time,))
                
                result = cursor.fetchone()
                evaluated, wins, avg_confidence = result if result else (0, 0, 0)
                
                win_rate = (wins / evaluated * 100) if evaluated > 0 else 0
                
                return {
                    'win_rate': win_rate,
                    'total_signals': evaluated,
                    'wins': wins or 0,
                    'avg_confidence': avg_confidence or 0,
                    'period_hours': hours
                }
                
        except Exception as e:
            print(f"Error getting rolling performance: {e}")
            return {}
    
    def update_strategy_performance(self):
        """Update aggregated strategy performance statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get all strategies with their performance
                cursor.execute("""
                SELECT 
                    s.strategy_name,
                    COUNT(s.id) as total_signals,
                    SUM(CASE WHEN sr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    SUM(CASE WHEN sr.result = 'LOSS' THEN 1 ELSE 0 END) as losses,
                    SUM(CASE WHEN sr.result = 'TIE' THEN 1 ELSE 0 END) as ties,
                    AVG(s.confidence) as avg_confidence
                FROM signals s
                LEFT JOIN signal_results sr ON s.signal_id = sr.signal_id
                GROUP BY s.strategy_name
                """)
                
                for row in cursor.fetchall():
                    strategy_name, total, wins, losses, ties, avg_conf = row
                    wins = wins or 0
                    losses = losses or 0
                    ties = ties or 0
                    evaluated = wins + losses + ties
                    win_rate = (wins / evaluated * 100) if evaluated > 0 else 0
                    
                    cursor.execute("""
                    INSERT OR REPLACE INTO strategy_performance (
                        strategy_name, total_signals, wins, losses, ties,
                        win_rate, avg_confidence, last_updated
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        strategy_name, total, wins, losses, ties,
                        win_rate, avg_conf or 0, datetime.now()
                    ))
                
                conn.commit()
                
        except Exception as e:
            print(f"Error updating strategy performance: {e}")
    
    def create_performance_snapshot(self):
        """Create a daily performance snapshot for historical tracking."""
        try:
            stats = self.get_performance_stats(days=1)  # Last 24 hours
            overall = stats.get('overall', {})
            strategies = stats.get('by_strategy', [])
            
            if not overall:
                return
            
            best_strategy = max(strategies, key=lambda x: x['win_rate']) if strategies else None
            worst_strategy = min(strategies, key=lambda x: x['win_rate']) if strategies else None
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                INSERT INTO performance_snapshots (
                    snapshot_date, total_signals, total_wins, overall_win_rate,
                    best_strategy, best_strategy_win_rate, worst_strategy,
                    worst_strategy_win_rate, avg_confidence, snapshot_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    datetime.now().date(),
                    overall.get('total_signals', 0),
                    overall.get('wins', 0),
                    overall.get('win_rate', 0),
                    best_strategy['strategy_name'] if best_strategy else None,
                    best_strategy['win_rate'] if best_strategy else 0,
                    worst_strategy['strategy_name'] if worst_strategy else None,
                    worst_strategy['win_rate'] if worst_strategy else 0,
                    overall.get('avg_confidence', 0),
                    json.dumps(stats)
                ))
                
                conn.commit()
                
        except Exception as e:
            print(f"Error creating performance snapshot: {e}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics and health information."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get table counts
                cursor.execute("SELECT COUNT(*) FROM signals")
                total_signals = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM signal_results")
                total_results = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM strategy_performance")
                total_strategies = cursor.fetchone()[0]
                
                # Get database file size
                db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
                
                return {
                    'total_signals': total_signals,
                    'total_results': total_results,
                    'total_strategies': total_strategies,
                    'evaluation_rate': (total_results / total_signals * 100) if total_signals > 0 else 0,
                    'database_size_mb': db_size / (1024 * 1024),
                    'database_path': self.db_path
                }
                
        except Exception as e:
            print(f"Error getting database stats: {e}")
            return {}
