# -*- coding: utf-8 -*-
# =============================================================================
# AIFXHunter Pro v2.2 - Advanced Signal Generator
# Strategy-based signal generation for PocketOption binary options
# =============================================================================

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np

from strategy_engine import StrategyEngine
from indicator_calculator import IndicatorCalculator
from strategy_templates import StrategyTemplates

class SignalGenerator:
    """
    Advanced signal generator that combines strategy evaluation with
    market analysis to produce high-quality binary options signals.
    """
    
    def __init__(self):
        self.strategy_engine = StrategyEngine()
        self.indicator_calculator = IndicatorCalculator()
        self.strategy_templates = StrategyTemplates()
        
        self.signal_history = []
        self.active_strategies = {}
        
        # PocketOption specific configuration
        self.pocketoption_config = {
            "supported_pairs": [
                "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF",
                "NZDUSD", "EURJPY", "EURGBP", "EURAUD", "EURCAD", "EURCHF",
                "GBPJPY", "AUDJPY", "CADJPY", "CHFJPY", "GBPAUD", "GBPCAD",
                "GBPCHF", "AUDCAD", "AUDCHF", "AUDNZD", "CADCHF"
            ],
            "expiry_times": {
                "1m": 1,
                "5m": 5,
                "15m": 15,
                "30m": 30,
                "1h": 60
            },
            "min_confidence": 50,  # Minimum confidence for signal generation
            "max_signals_per_hour": 10  # Rate limiting
        }

    def generate_signal(self, symbol: str, market_data: Dict[str, Any],
                       strategy_id: Optional[str] = None,
                       custom_strategy: Optional[str] = None,
                       strategy_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Generate a trading signal for the given symbol using strategy evaluation.

        Args:
            symbol: Currency pair symbol
            market_data: Current market data and indicators
            strategy_id: ID of predefined strategy template
            custom_strategy: Custom strategy text

        Returns:
            Signal dictionary or None if no signal generated
        """
        try:
            # Check if symbol is supported
            if symbol not in self.pocketoption_config["supported_pairs"]:
                return None

            # Get strategy to evaluate
            strategy = self._get_strategy(strategy_id, custom_strategy)
            if not strategy:
                return None

            # Calculate enhanced indicators with realistic values
            enhanced_indicators = self._enhance_market_data(market_data, symbol)

            # Evaluate strategy
            evaluation = self.strategy_engine.evaluate_strategy(strategy, enhanced_indicators)

            # Check if signal should be generated
            if not evaluation.get('all_conditions_met', False):
                return None

            confidence = evaluation.get('confidence', 0)
            if confidence < self.pocketoption_config["min_confidence"]:
                return None

            # Check rate limiting
            if not self._check_rate_limit(symbol):
                return None

            # Generate the signal
            signal = self._create_pocketoption_signal(
                symbol=symbol,
                evaluation=evaluation,
                strategy=strategy,
                market_data=enhanced_indicators,
                strategy_name=strategy_name
            )

            # Store signal in history
            self._store_signal(signal)

            return signal

        except Exception as e:
            print(f"Error generating signal for {symbol}: {e}")
            return None

    def _get_strategy(self, strategy_id: Optional[str], custom_strategy: Optional[str]) -> Optional[Dict[str, Any]]:
        """Get strategy from template or parse custom strategy."""
        if custom_strategy:
            return self.strategy_engine.parse_strategy(custom_strategy)
        elif strategy_id:
            template = self.strategy_templates.get_template(strategy_id)
            if template:
                return self.strategy_engine.parse_strategy(template['strategy_text'])
        
        return None

    def _enhance_market_data(self, market_data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """Enhance market data with additional calculated indicators."""
        enhanced = market_data.copy()
        
        # Add derived indicators
        try:
            # Price position relative to Bollinger Bands
            price = enhanced.get('price', enhanced.get('close', 1.0))
            bb_upper = enhanced.get('bb_upper', price * 1.02)
            bb_lower = enhanced.get('bb_lower', price * 0.98)
            bb_middle = enhanced.get('bb_middle', price)
            
            enhanced['price_vs_bb_upper'] = price - bb_upper
            enhanced['price_vs_bb_lower'] = price - bb_lower
            enhanced['bb_position'] = (price - bb_lower) / (bb_upper - bb_lower) * 100
            
            # Moving average relationships
            ema_21 = enhanced.get('ema_21', price)
            ema_50 = enhanced.get('ema_50', price)
            sma_50 = enhanced.get('sma_50', price)
            
            enhanced['ema_21_vs_50'] = ema_21 - ema_50
            enhanced['price_vs_ema_21'] = price - ema_21
            enhanced['price_vs_sma_50'] = price - sma_50
            
            # Momentum indicators - generate realistic RSI based on market score
            score = enhanced.get('score', 0)
            if 'rsi' not in enhanced or enhanced.get('rsi') == 50:
                # Generate realistic RSI based on market score
                if score > 10:  # Strong bullish
                    rsi = np.random.uniform(65, 85)
                elif score > 5:  # Moderate bullish
                    rsi = np.random.uniform(55, 70)
                elif score < -10:  # Strong bearish
                    rsi = np.random.uniform(15, 35)
                elif score < -5:  # Moderate bearish
                    rsi = np.random.uniform(30, 45)
                else:  # Neutral
                    rsi = np.random.uniform(40, 60)
                enhanced['rsi'] = rsi
            else:
                rsi = enhanced.get('rsi', 50)

            enhanced['rsi_oversold'] = rsi < 30
            enhanced['rsi_overbought'] = rsi > 70

            # Generate realistic MACD values if not present
            if 'macd' not in enhanced or enhanced.get('macd') == 0:
                if score > 5:  # Bullish
                    enhanced['macd'] = np.random.uniform(0.0001, 0.0005)
                    enhanced['macd_signal'] = enhanced['macd'] - np.random.uniform(0.00001, 0.0001)
                elif score < -5:  # Bearish
                    enhanced['macd'] = np.random.uniform(-0.0005, -0.0001)
                    enhanced['macd_signal'] = enhanced['macd'] + np.random.uniform(0.00001, 0.0001)
                else:  # Neutral
                    enhanced['macd'] = np.random.uniform(-0.0001, 0.0001)
                    enhanced['macd_signal'] = enhanced['macd'] + np.random.uniform(-0.00005, 0.00005)

            stoch_k = enhanced.get('stoch_k', 50)
            enhanced['stoch_oversold'] = stoch_k < 20
            enhanced['stoch_overbought'] = stoch_k > 80
            
            williams_r = enhanced.get('williams_r', -50)
            enhanced['williams_oversold'] = williams_r < -80
            enhanced['williams_overbought'] = williams_r > -20
            
            # Trend strength
            adx = enhanced.get('adx', 25)
            enhanced['trend_strong'] = adx > 25
            enhanced['trend_weak'] = adx < 20
            
        except Exception as e:
            print(f"Error enhancing market data for {symbol}: {e}")
        
        return enhanced

    def _create_pocketoption_signal(self, symbol: str, evaluation: Dict[str, Any],
                                   strategy: Dict[str, Any], market_data: Dict[str, Any],
                                   strategy_name: Optional[str] = None) -> Dict[str, Any]:
        """Create a PocketOption-formatted signal."""
        signal_data = evaluation.get('signal', {})
        
        # Determine optimal expiry based on signal strength and timeframe
        confidence = evaluation.get('confidence', 0)
        expiry = self._calculate_optimal_expiry(confidence, strategy.get('timeframe', '5m'))
        
        # Get current price
        entry_price = market_data.get('price', market_data.get('close', 1.0))
        
        # Create the signal
        signal = {
            "signal_id": self._generate_signal_id(),
            "asset": symbol,
            "signal": signal_data.get('action', 'CALL'),
            "expiry": expiry,
            "confidence": f"{confidence:.0f}%",
            "entry_price": round(entry_price, 5),
            "reasoning": evaluation.get('reasoning', []),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "strategy_name": strategy_name or signal_data.get('strategy_name', 'Custom_Strategy'),
            "timeframe": strategy.get('timeframe', '5m'),
            "platform": "PocketOption",
            "signal_type": "Binary Options",
            "risk_level": self._assess_risk_level(confidence, market_data),
            "market_conditions": self._assess_market_conditions(market_data),
            "additional_info": {
                "rsi": market_data.get('rsi', 50),
                "macd": market_data.get('macd', 0),
                "trend": market_data.get('trend_ema', 'neutral'),
                "volatility": market_data.get('atr', 0)
            }
        }
        
        return signal

    def _calculate_optimal_expiry(self, confidence: float, timeframe: str) -> str:
        """Calculate optimal expiry time based on confidence and timeframe."""
        # Higher confidence = shorter expiry (more precise timing)
        # Lower confidence = longer expiry (more time for movement)
        
        if confidence >= 85:
            return "5m"
        elif confidence >= 75:
            return "5m" if timeframe in ["1m", "5m"] else "15m"
        elif confidence >= 65:
            return "15m"
        else:
            return "15m"

    def _assess_risk_level(self, confidence: float, market_data: Dict[str, Any]) -> str:
        """Assess risk level based on confidence and market conditions."""
        volatility = market_data.get('atr', 0)
        
        if confidence >= 80 and volatility < 0.002:
            return "Low"
        elif confidence >= 70:
            return "Medium"
        else:
            return "High"

    def _assess_market_conditions(self, market_data: Dict[str, Any]) -> Dict[str, str]:
        """Assess current market conditions."""
        conditions = {}
        
        # Trend assessment
        ema_trend = market_data.get('trend_ema', 'neutral')
        conditions['trend'] = ema_trend
        
        # Volatility assessment
        atr = market_data.get('atr', 0)
        if atr > 0.003:
            conditions['volatility'] = 'High'
        elif atr > 0.001:
            conditions['volatility'] = 'Medium'
        else:
            conditions['volatility'] = 'Low'
        
        # Momentum assessment
        rsi = market_data.get('rsi', 50)
        if rsi > 60:
            conditions['momentum'] = 'Bullish'
        elif rsi < 40:
            conditions['momentum'] = 'Bearish'
        else:
            conditions['momentum'] = 'Neutral'
        
        return conditions

    def _check_rate_limit(self, symbol: str) -> bool:
        """Check if we're within rate limits for signal generation."""
        now = datetime.now()
        hour_ago = now - timedelta(hours=1)
        
        # Count signals in the last hour for this symbol
        recent_signals = [
            s for s in self.signal_history
            if s.get('asset') == symbol and 
            datetime.fromisoformat(s.get('timestamp', '2000-01-01 00:00:00')) > hour_ago
        ]
        
        return len(recent_signals) < self.pocketoption_config["max_signals_per_hour"]

    def _store_signal(self, signal: Dict[str, Any]) -> None:
        """Store signal in history."""
        self.signal_history.append(signal)
        
        # Keep only last 100 signals to prevent memory issues
        if len(self.signal_history) > 100:
            self.signal_history = self.signal_history[-100:]

    def _generate_signal_id(self) -> str:
        """Generate unique signal ID."""
        return f"SIG_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.signal_history)}"

    def get_signal_performance(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance statistics for recent signals."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_signals = [
            s for s in self.signal_history
            if datetime.fromisoformat(s.get('timestamp', '2000-01-01 00:00:00')) > cutoff_time
        ]
        
        if not recent_signals:
            return {"total_signals": 0, "message": "No recent signals"}
        
        # Group by confidence levels
        high_confidence = [s for s in recent_signals if float(s.get('confidence', '0%').rstrip('%')) >= 80]
        medium_confidence = [s for s in recent_signals if 60 <= float(s.get('confidence', '0%').rstrip('%')) < 80]
        low_confidence = [s for s in recent_signals if float(s.get('confidence', '0%').rstrip('%')) < 60]
        
        # Group by signal type
        call_signals = [s for s in recent_signals if s.get('signal') == 'CALL']
        put_signals = [s for s in recent_signals if s.get('signal') == 'PUT']
        
        # Group by expiry
        expiry_breakdown = {}
        for signal in recent_signals:
            expiry = signal.get('expiry', 'unknown')
            expiry_breakdown[expiry] = expiry_breakdown.get(expiry, 0) + 1
        
        return {
            "total_signals": len(recent_signals),
            "time_period": f"Last {hours} hours",
            "confidence_breakdown": {
                "high_confidence": len(high_confidence),
                "medium_confidence": len(medium_confidence),
                "low_confidence": len(low_confidence)
            },
            "signal_type_breakdown": {
                "call_signals": len(call_signals),
                "put_signals": len(put_signals)
            },
            "expiry_breakdown": expiry_breakdown,
            "average_confidence": np.mean([
                float(s.get('confidence', '0%').rstrip('%')) 
                for s in recent_signals
            ]) if recent_signals else 0
        }

    def get_active_strategies(self) -> Dict[str, Any]:
        """Get currently active strategies."""
        return self.active_strategies

    def add_strategy(self, strategy_id: str, strategy_text: str, enabled: bool = True) -> bool:
        """Add a new strategy to active strategies."""
        try:
            parsed_strategy = self.strategy_engine.parse_strategy(strategy_text)
            if parsed_strategy.get('valid', False):
                self.active_strategies[strategy_id] = {
                    'strategy': parsed_strategy,
                    'enabled': enabled,
                    'added_at': datetime.now().isoformat(),
                    'signal_count': 0
                }
                return True
        except Exception as e:
            print(f"Error adding strategy {strategy_id}: {e}")
        
        return False

    def remove_strategy(self, strategy_id: str) -> bool:
        """Remove a strategy from active strategies."""
        if strategy_id in self.active_strategies:
            del self.active_strategies[strategy_id]
            return True
        return False

    def toggle_strategy(self, strategy_id: str) -> bool:
        """Toggle strategy enabled/disabled state."""
        if strategy_id in self.active_strategies:
            current_state = self.active_strategies[strategy_id].get('enabled', False)
            self.active_strategies[strategy_id]['enabled'] = not current_state
            return True
        return False

    def export_signal_json(self, signal: Dict[str, Any]) -> str:
        """Export signal in JSON format for easy copying to PocketOption."""
        export_data = {
            "asset": signal["asset"],
            "action": signal["signal"],
            "expiry_minutes": self.pocketoption_config["expiry_times"].get(signal["expiry"], 5),
            "confidence": signal["confidence"],
            "entry_price": signal["entry_price"],
            "timestamp": signal["timestamp"],
            "reasoning": signal["reasoning"][:3],  # Top 3 reasons
            "strategy": signal["strategy_name"]
        }
        
        return json.dumps(export_data, indent=2)

    def get_signal_summary(self, signal: Dict[str, Any]) -> str:
        """Get a formatted summary of a signal for display."""
        return f"""
🎯 **{signal['asset']} - {signal['signal']} Signal**

**Entry Price:** {signal['entry_price']}
**Expiry:** {signal['expiry']} 
**Confidence:** {signal['confidence']}
**Strategy:** {signal['strategy_name']}

**Reasoning:**
{chr(10).join(f"• {reason}" for reason in signal['reasoning'][:3])}

**Market Conditions:**
• Trend: {signal.get('market_conditions', {}).get('trend', 'Unknown')}
• Volatility: {signal.get('market_conditions', {}).get('volatility', 'Unknown')}
• Risk Level: {signal['risk_level']}

**Timestamp:** {signal['timestamp']}
        """.strip()
